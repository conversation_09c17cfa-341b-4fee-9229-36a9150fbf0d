{"scripts": {"tailwind-dev": "npx @tailwindcss/cli -i ./tailwind-input.css -o ./static/tailwind.css --watch", "tailwind-build": "npx @tailwindcss/cli -i ./tailwind-input.css -o ./static/tailwind.css"}, "devDependencies": {"autoprefixer": "^10.4.21", "tailwindcss": "^4.1.12", "vite-plugin-vue-devtools": "^7.7.2"}, "dependencies": {"@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.1.12", "crypto-js": "^4.2.0", "luch-request": "^3.1.1", "postcss": "^8.5.6", "weixin-js-sdk": "^1.6.5"}}