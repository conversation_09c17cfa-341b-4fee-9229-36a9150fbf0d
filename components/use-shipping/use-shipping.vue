<template>
  <uni-popup ref="popup" type="bottom">
    <view class="bg-drak h-[30vh]">
      <view v-if="!expressData" class="text-center pt-30">暂无快递信息</view>
      <!-- 空白页 -->
      <view v-else>
        <view class=" padding margin-bottom-sm bg-main">
          <view class="fs-lg fwb">{{ expressData.expressCompanyName }}</view>
          <view>运单号：{{ expressData.expressNo }}
            <text class="copy" @click="copy">复制</text>
          </view>
        </view>
      </view>
      <view v-if="expressData && expressData.data">
        <view class="product border-radius padding margin-bottom-sm bg-main" style="padding-bottom: 15rpx;">
          <view :class="{ 'active': index === 0, 'fwb': index === 0 }" class="dflex item pos-r"
                v-for="(item, index) in expressData.data" :key="index">
            <view :class="{ 'active': index === 0 }" class="circle"></view>
            <view :class="{ 'ft-dark': index > 0 }" class="margin-left-lg pos-r w-full margin-bottom">
              <view>{{ item.context }}</view>
              <view class="margin-top-xs fs-xs">{{ item.time }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import {ref} from 'vue'
import {getOrderExpressData} from "../../common/api/express";

const popup = ref()
const expressData = ref()
const copy = () => {
  uni.setClipboardData({
    data: expressData.value.expressNo,
    success: function (res) {
      uni.getClipboardData({
        success: function (res) {
          uni.showToast({
            title: '复制成功'
          });
        }
      });
    }
  });
}
const init = async (orderId) => {
  try {
    expressData.value = await getOrderExpressData(orderId)
  } catch (e) {
  } finally {
    popup.value.open()
  }
}

defineExpose({init})
</script>


<style scoped lang="scss">
.copy {
  margin-left: 30rpx;
  padding: 10rpx 40rpx;
  background-color: #f1f1f1;
  border-radius: 40rpx;
  font-size: 24rpx;
}

.item {
  align-items: baseline;
}

.item:not(:last-child)::before {
  content: ' ';
  border-left: 1px solid #d3d3d3;
  position: absolute;
  bottom: -14rpx;
  top: 14rpx;
  left: 10rpx;
  border-left-color: rgb(211, 211, 211);
}

.item.active::before {
  border-left: 1px solid #ff6a6c;
}

.circle {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  background: #d3d3d3;
  border-radius: 50%;
  top: 14rpx;
}

.circle.active {
  background: #ff6a6c !important;
  transform: scale(1.1);
}

.circle.active::after {
  content: ' ';
  background: rgba(255, 106, 108, 0.5) !important;
  -webkit-transform: scale(1.6);
  transform: scale(1.6);
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  border-radius: 50%;
}
</style>
