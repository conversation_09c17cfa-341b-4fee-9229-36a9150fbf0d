<template>
  <uni-popup ref="popup" type="bottom">
    <view class="container bg-main pos-r">
      <view class="padding-xl dflex-c dflex-flow-c">
        <view class="portrait-box margin-bottom">
          <image class="headimg border-radius-c"
                 src="/static/images/logo-mini.png"></image>
        </view>

        <view class="w-full dflex padding-bottom-sm">
          <view class="iconfont iconshouji margin-right"></view>
          <view class="flex1 dflex">
            <input class="border-line padding-sm flex1" type="number" maxlength="11" v-model="form.mobile"
                   placeholder="请输入手机号"/>
            <!--<view  class="padding-tb-sm ft-dark" >获取</view>-->
          </view>
        </view>

        <view v-if="loginType==='sms'" class="w-full dflex padding-bottom-sm">
          <view class="iconfont iconyanzheng margin-right"></view>
          <view class="border-line flex1 dflex">
            <input class="padding-sm flex1" type="number" v-model="form.code" maxlength="4" placeholder="请输入验证码"/>
            <view v-if="timerStore.smsCountDown < 1" class="padding-tb-sm ft-base" @click="handleSmsSend()">发送验证码
            </view>
            <view v-else class="padding-tb-sm ft-base">{{ timerStore.smsCountDown }}s 重新获取</view>
          </view>
        </view>

        <!--已禁用-->
        <view v-if="loginType==='password'" class="w-full dflex">
          <view class="iconfont iconmima margin-right"></view>
          <input
            class="border-line padding-sm flex1"
            type="password"
            maxlength="20"
            v-model="form.password"
            @confirm="doLogin()"
            placeholder="请输入密码"
          />
        </view>
        <view v-if="loginType==='password'" class="dflex-b w-full margin-bottom-sm">
          <view class="padding-tb-sm ft-dark" @click="toForget">忘记密码</view>
          <view class="padding-tb-sm  text-mint" @click="$refs.regPopup.show()">立即注册</view>
        </view>
        <view class="w-full mt-10">
          <view class="dflex-b border-radius-lg">
            <view class="tac padding-tb-sm flex1 bg-mint text-white fs" @click="doLogin">登录</view>
          </view>
        </view>
        <view class="w-full margin-top-xl">
          <view class="dflex-b border-radius-lg">
            <view class="tac padding-tb-sm flex1 bg-mint-light fs" @click="userStore.hideLogin()">取消</view>
          </view>
        </view>
      </view>
    </view>

    <use-register ref="regPopup"></use-register>
  </uni-popup>
</template>
<script setup>
import {ref, reactive, watch, onMounted} from 'vue'
import {loginByPassword, loginBySMS} from "@/common/api/login"
import {useUserStore} from "@/stores/user"
import {useTimerStore} from "@/stores/timer";
import {getShareCodeData} from "@/common/api/distribution"
import {sendSms} from "@/common/api";

const userStore = useUserStore()
const timerStore = useTimerStore()
const popup = ref(null)
const loading = ref(false)
/*登录方式: sms\password*/
const loginType = ref('sms')
const form = reactive({
  mobile: '',
  password: '',
  code: ''
})
const toForget = () => {
  // 忘记密码
  uni.navigateTo({
    url: '/pages/login/forgot-password'
  });
}
const doLoginBySms = async () => {
  if (!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)) {
    uni.showToast({title: '请输入正确的手机号', icon: 'none'})
    return;
  }

  if (!form.code) {
    uni.showToast({title: '请输入短信验证码', icon: 'none'})
    return;
  }

  const loginRes = await loginBySMS({mobile: form.mobile, code: form.code})
  if (loginRes.statusCode === 200) {
    uni.showToast({title: '登录成功'})
    userStore.hideLogin()
    const shareCode = uni.getStorageSync("fromShareCode")
    getShareCodeData(shareCode).then(res => {
      uni.setStorageSync('shareCode', res)
    })
  }
}
const doLogin = () => {
  if (loginType.value === 'password') {
    doLoginByPassword()
  }
  if (loginType.value === 'sms') {
    doLoginBySms()
  }
}

const doLoginByPassword = async () => {
  if (!form.mobile) {
    uni.showToast({title: '请输入手机号', icon: 'none'})
    return;
  }

  if (!form.password) {
    uni.showToast({title: '请输入密码', icon: 'none'})
    return;
  }

  const loginRes = await loginByPassword({username: form.mobile, password: form.password})
  if (loginRes.statusCode === 200) {
    uni.showToast({title: '登录成功'})
    userStore.hideLogin()
    const shareCode = uni.getStorageSync("fromShareCode")
    getShareCodeData(shareCode).then(res => {
      uni.setStorageSync('shareCode', res)
    })
  }
}

const handleSmsSend = async () => {
  if (!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)) {
    uni.showToast({title: '请输入正确的手机号', icon: 'none'})
    return;
  }

  if (loading.value) {
    return
  }
  loading.value = true
  try {
    await sendSms(form.mobile)
    uni.showToast({title: '发送成功'})
    timerStore.smsCountDownStart(60)
  } catch (e){
    uni.showToast({title: e.msg, icon: 'error'})
  } finally {
    loading.value = false
  }
}

watch(() => userStore.loginVisible, (newVal) => {
  if (newVal) {
    popup.value.open()
  } else {
    popup.value.close()
  }
},)
onMounted(() => {
  if (userStore.loginVisible) {
    popup.value.open()
  }
})
</script>
<style lang="scss">
page {
  background: #f4f4f4;
}

.container {
  padding-top: 5vh;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;
}

.portrait-box {
  image {
    width: 130rpx;
    height: 130rpx;
    border: 5rpx solid #fff;
  }
}

.l-mask {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(51, 51, 51, 0.3);
  z-index: 99;
}

.box-container {
  position: absolute;
  width: 500rpx;
  height: 300rpx;
  background: #fff;
  left: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  z-index: 999;
  border-radius: 10rpx;
  text-align: center;
  padding: 30rpx;

  .title {
    margin-top: 40rpx;
    font-size: 30rpx;
  }

  .btn-contaer {
    display: flex;
    position: absolute;
    bottom: 30rpx;
    left: 30rpx;
    right: 30rpx;
  }

  button {
    background: #eee;
    color: #333;
    width: 50%;
    font-size: 28rpx;

    &:last-child {
      margin-left: 10px;
      background: #26a92e;
      color: #fff;
    }
  }
}
</style>
