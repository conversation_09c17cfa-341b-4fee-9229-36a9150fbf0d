<template>
  <view class="container">
    <!-- 订单状态区 -->
    <!-- <scroll-view scroll-x="true" class="navbar-area bg-main" :scroll-left="scrollLeft"> -->
    <view class="state-area dflex navbar-area bg-main">
      <view class="nav-item dflex-c pos-r fs h-full text-sm" :class="{ active: tabCurrentIndex === index }"
            v-for="(item, index) in navList" :key="index" @click="tabCurrentIndex = index">
        {{ item.state }}
      </view>
    </view>
    <!-- </scroll-view> -->

    <!-- 订单轮播区 -->
    <swiper class="swiper-area w-full" :duration="0" :current="tabCurrentIndex" @change="changeTab">
      <!-- 轮播项对应订单状态 -->
      <swiper-item class="tab-content wh-full" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
        <!-- 滚动区 -->
        <scroll-view class="h-full" scroll-y @scrolltolower="nextPage">
          <!-- 空白页 -->
          <use-empty v-if="tabItem.orderList.length === 0 && tabItem.loaded" e-style="round" e-type="cart"
                     tip="订单数据为空" height="93vh"></use-empty>
          <!-- 订单列表区 -->
          <view class="padding-lr margin-bottom-sm" :class="index === 0 ? 'padding-top-sm' : ''"
                v-for="(item, index) in tabItem.orderList" :key="index">
            <!-- 订单项 -->
            <view class="order-item padding bg-main border-radius">
              <view @click="toDetail(item.id)">
                <!-- 订单商品明细 -->
                <template v-for="(orderItem, idx) in item.orderItemList" :key="idx">
                  <view class="goods-area" :class="{ 'margin-top': index > 0 }" @click="doBuyAgain(orderItem.goodsId)">
                    <image :src="orderItem.goodsImage" mode="aspectFill"></image>
                    <view class="right flex1">
                      <text class="clamp-2">{{ orderItem.goodsName }}
                        {{ orderItem.goodsSkuName }}
                      </text>
                      <view class="ft-dark fs-xs padding-top-xs">
                        <text class="margin-right">× {{ orderItem.quantity }}</text>
                        {{ orderItem.goodsSkuName || '&nbsp;&nbsp;' }}
                      </view>
                      <view class="margin-top-sm">
                        <text class="price ft-main fs-sm">{{ orderItem.price }}</text>
                      </view>
                    </view>
                  </view>
                </template>

                <!-- 实付款 -->
                <view class="dflex-e">
                  <text class="fs-xs margin-right-xs">实付款</text>
                  <text class="price ft-main">{{ item.totalPrice }}</text>
                </view>
              </view>

              <!-- 订单操作区 -->
              <view class="dflex-b margin-top-sm">
                <view>
                  <!-- 当前状态 -->
                  <text class="ft-dark">{{ getOrderStatusText(item.status) }}</text>
                </view>

                <view class="dflex-e">
                  <view class="dflex gap-1.5">
                    <template v-for="action in getOrderActions(item.status)" :key="action">
                      <button class="bg-mint border-radius-big text-white" size="mini" hover-class="bg-mint/90" @click="handleAction(action, item.id)" v-if="action !== '去评价' || !item.commentStatus">{{ action }}</button>
                    </template>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 上拉加载更多 -->
          <use-loadmore v-if="tabItem.orderList.length > 0 && tabItem.loaded && tabItem.hasmore"
                        :type="tabItem.loadingType"></use-loadmore>
        </scroll-view>
      </swiper-item>
    </swiper>

    <use-shipping ref="shippingDialog"></use-shipping>
  </view>
</template>
<script setup>
import {ref, watch} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getOrderStatusText, getOrderActions} from '/common/utils/orderStatus'
import {getOrderListPageData, postOrderCancelData, deleteOrderData, postOrderReceiptData} from '/common/api/order'

const shippingDialog = ref()

const tabCurrentIndex = ref(0)
const navList = ref([{
  id: 0,
  state: '全部',
  page: 1,
  loadingType: 'more',
  orderList: []
},
  {
    id: 1,
    state: '待付款',
    page: 1,
    loadingType: 'more',
    orderList: []
  },
  {
    id: 2,
    state: '待发货',
    page: 1,
    loadingType: 'more',
    orderList: []
  },
  {
    id: 3,
    state: '待收货',
    page: 1,
    loadingType: 'more',
    orderList: []
  },
  {
    id: 4,
    state: '已完成',
    page: 1,
    loadingType: 'more',
    orderList: []
  },
  {
    id: 5,
    state: '已取消',
    page: 1,
    loadingType: 'more',
    orderList: []
  },
])

watch(tabCurrentIndex, () => {
  refresh()
})

const refresh = () => {
  const currentTab = navList.value[tabCurrentIndex.value]
  currentTab.page = 1
  currentTab.orderList = []
  loadData()
}

const nextPage = () => {
  const currentTab = navList.value[tabCurrentIndex.value]
  if (currentTab.loadingType === 'more') {
    currentTab.page++
    loadData()
  }
}

const changeTab = (e) => {
  tabCurrentIndex.value = e.detail.current
}

const loadData = async () => {
  const currentTab = navList.value[tabCurrentIndex.value]
  const res = await getOrderListPageData({page: currentTab.page, status: currentTab.id || null})
  currentTab.orderList.push(...res.records)
  if (res.current === res.pages) {
    currentTab.loadingType = 'nomore'
  }
}

const handleAction = (action, orderId) => {
  uni.showModal({
    title: '提示',
    content: `确定要${action}吗？`,
    success: (res) => {
      if (res.confirm) {
        switch (action) {
          case '取消订单':
            doCancel(orderId)
            break
          case '删除订单':
            doDelete(orderId)
            break
          case '确认收货':
            doReceipt(orderId)
            break
          case '查看物流':
            showShipping(orderId)
            break
          case '提醒发货':
            doShipping(orderId)
            break
          case '再次购买':
            doBuyAgain(orderId)
            break
          case '去评价':
            goComment(orderId)
            break
          case '去付款':
            toPayment(orderId)
            break
        }
      }
    }
  })
}

// 去付款
const toPayment = (id) => {
  uni.navigateTo({
    url: `/pages/order/pay?order_id=${id}`
  })
}
// 取消订单
const doCancel = async (id) => {
  await postOrderCancelData(id)
  refresh()
  uni.showToast({
    title: '取消成功'
  })
}
// 确认收货
const doReceipt = async (id) => {
  await postOrderReceiptData(id)
  refresh()
  uni.showToast({
    title: '收货成功'
  })
}
// 查看物流
const showShipping = (id) => {
  shippingDialog.value.init(id)
}
// 提醒发货
const doShipping = (id) => {
  uni.showToast({
    title: '提醒成功'
  })
}
// 再次购买
const doBuyAgain = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
// 删除订单
const doDelete = async (id) => {
  await deleteOrderData(id)
  refresh()
  uni.showToast({
    title: '删除成功'
  })
}
// 去评价
const goComment = (id) => {
  uni.navigateTo({
    url: `/pages/order/order-comment?id=${id}`
  })
}


const toDetail = (id) => {
}

onLoad((params) => {
  loadData()
})
</script>


<style lang="scss">
page,
.container {
  min-height: 100%;
  background: $page-color-base;
}

/* 订单状态区 */
.navbar-area {
  white-space: nowrap;
}

.state-area {
  height: 7vh;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
  z-index: 10;
  top: 0;
}

.nav-item {
  flex: 1;

  &.active {
    &:after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      bottom: 0;
      width: 44px;
      height: 0;
      border-bottom: 2px solid $base-color;
    }
  }
}

/* 订单轮播区 */
.swiper-area {
  height: 93vh;
}

/* 订单区 */
.order-area {
}


/* 订单项 */
.order-item {

  /* 订单商品明细区 */
  .goods-area {
    display: flex;

    image {
      width: 180rpx;
      height: 180rpx;
    }

    .right {
      padding: 0 30upx 0 24upx;
      overflow: hidden;

      .attr-box {
        font-size: $font-sm + 2upx;
        color: $font-color-light;
        padding: 10upx 12upx;
      }
    }
  }

  /* 操作按钮 */
  .action-btn {
    width: 156rpx;
    height: inherit;
    line-height: inherit;
    margin: 0 0 0 20rpx;
    padding: 12rpx 0;
    font-size: $font-sm + 2upx;
    color: $font-color-dark;


    &.main-btn {
      background-color: #c8ded8;
      color: $base-color;

    }
  }
}
</style>
