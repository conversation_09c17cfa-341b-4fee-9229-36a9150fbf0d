<template>
  <view>
    <!-- 收货人 -->
    <view class="gap"></view>
    <use-list-title v-if="!address" color="#333" title="选择收货人" iconfont="icondizhi-"
                    @goto="toAddress"></use-list-title>
    <view v-else class="padding dflex-b bg-main" @click="toAddress">
      <view class="dflex">
        <view class="iconfont icondizhi- margin-right ft-main"></view>
        <view class="w-full dflex-wrap-w">
          <view class="margin-bottom-xs">
            <text>{{ address.province }} {{ address.city }}{{ address.district }}{{ address.address }}</text>
          </view>
          <view>
            <text>{{ address.consignee }}</text>
            <text class="margin-left">{{ address.telephone }}</text>
          </view>
        </view>
      </view>

      <view class="iconfont iconjiantou-01 fs-sm"></view>
    </view>
    <view class="gap"></view>

    <view class="goods-area bg-main padding">
      <!-- 商品列表 -->
      <view class="goods-item" :class="{ 'margin-top': index > 0 }" v-for="(item, index) in goodsList"
            :key="index">
        <view class="pos-r">
          <image mode="aspectFill" :src="item.goodsImage"></image>
          <view v-if="(item.stock < 10 || item.stock < item.quantity)"
                class="disabled dflex-c dflex-flow-c pos-a pos-tl-c border-radius-c">
            <text>库存不足</text>
            <text class="margin-left-xs fs-xs" v-if="item.stock > 0">剩余
              {{ item.stock }}
            </text>
          </view>
        </view>
        <view class="flex1 padding-left-sm">
          <text class="title clamp-2">{{ item.goodsName || '' }} {{ item.skuName || '' }}</text>
          <view class="ft-dark fs-xs padding-top-xs">
            <text class="margin-right">× {{ item.quantity }}</text>
            {{ (item.skuName) || '&nbsp;&nbsp;' }}
          </view>
          <view class="pos-r dflex-b padding-top">
            <view class="price flex1">{{ item.actualPrice || '' }}</view>

            <!-- + - 数量 -->
            <use-number-box :min="1" :max="item.stock"
                            :value="item.quantity > item.stock ? item.stock : item.quantity"
                            :is-max="item.quantity >= item.stock" :is-min="item.quantity === 1"
                            :index="index" direction="right" @eventChange="numberChange"></use-number-box>
          </view>
        </view>
      </view>
    </view>
    <view class="gap"></view>

    <!-- 优惠券 -->
    <use-list-title v-if="false" title="优惠券" :tip="couponName" color="#333" iconfont="iconyouhui"
                    @click="$refs.couponPopup.open()">
    </use-list-title>
    <!-- 优惠券弹出层 -->
    <uni-popup type="bottom" ref="couponPopup">
      <!-- 优惠券区 -->
      <view class="coupon-area padding bg-drak">
        <view class="coupon-item bg-main pos-r fs-xs" v-for="(item, index) in couponList" :key="index">
          <view class="content pos-r padding dflex-b">
            <view class="">
              <view class="margin-bottom-xs fs">{{ item.name }}</view>
              <view class="ft-dark">有效期至 {{ item.end_time.split(' ')[0] }}</view>
            </view>
            <view class="tar">
              <view class="margin-bottom-xs price">{{ item.price }}</view>
              <view v-if="item.order_amount > 0" class="ft-dark">满{{ item.order_amount }}可用</view>
              <view v-else class="ft-dark">不限</view>
            </view>

            <view class="circle l"></view>
            <view class="circle r"></view>
          </view>
          <view class="dflex-b">
            <text class="ft-dark padding-lr">{{ item.type }}</text>
            <text class="ft-base padding-tb-sm padding-lr" @click="couponUse(item)">立即使用</text>
          </view>
        </view>
        <view v-if="!couponList || couponList.length <= 0" class="coupon-none">
          <text class="coupon-none-tip">开发中!!!!!</text>
        </view>
      </view>
    </uni-popup>
    <view class="gap"></view>

    <!-- 金额明细 -->
    <view class="bg-main">
      <view class="dflex-b padding-lr padding-tb-sm">
        <view class="flex1">总金额</view>
        <view class="">
          <text style="font-size: 24rpx;">￥</text>
          {{ totalMoney }}
        </view>
      </view>

      <view v-if="total_coupon_money > 0" class="dflex-b padding-lr padding-tb-sm">
        <view class="flex1">优惠金额</view>
        <view class="ft-base">-￥{{ total_coupon_money }}</view>
      </view>

      <view class="dflex-b padding-lr padding-tb-sm">
        <view class="margin-right-xl">备注</view>
        <input class="flex1 padding-sm" type="text" v-model="orderRemark" placeholder="请填写买家备注"
               placeholder-class="placeholder"/>
      </view>
    </view>
    <view class="gap"></view>

    <!-- 底部  -->
    <view class="oper-area pos-f pos-bottom w-full dflex-b bg-main safe-area-inset-bottom padding-left">
      <view>
        <text class="fs-sm">实付款</text>
        <text class="price margin-left-sm fs-xl">{{ totalMoney }}</text>
      </view>
      <view class="submit dflex-c bg-base fs animated-all" :class=" loading ? 'bg-disabled' : ''"
            @click="submit">提交订单
      </view>
    </view>
  </view>
</template>


<script setup>
import {ref, computed} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getAddressDefaultData} from "@/common/api/address"
import {getGoodsSkuDetailData, getActrualPrice} from "@/common/api/goods"
import {getCartListBySkuIdsData} from "@/common/api/cart";
import {putOrderData} from "@/common/api/order";

const loading = ref(false)
const address = ref(null)
const skuId = ref(0)
const cartSkuIds = ref([])
const couponName = ref('')
const couponList = ref([])
const couponPopup = ref(null)
const orderRemark = ref('')
const total_coupon_money = ref(0)

const totalMoney = computed(() => {
  let total = 0
  goodsList.value.forEach((item) => {
    total += item.actualPrice * item.quantity
  })
  return total
})

const goodsList = ref([])

const getAddress = async () => {
  address.value = await getAddressDefaultData()
}
const listBySkuId = async () => {
  goodsList.value = [await getGoodsSkuDetailData(skuId.value)]
}

const listByCart = async () => {
  goodsList.value = await getCartListBySkuIdsData(cartSkuIds.value)
}

const couponUse = () => {
}


const toAddress = () => {
  uni.$on('handleAddressSelect', (data) => {
    address.value = data
  })
  uni.navigateTo({
    url: '/pages/address/address-list'
  })
}

const submit = async () => {
  const orderData = {
    consignee: address.value.consignee,
    telephone: address.value.telephone,
    province: address.value.province,
    city: address.value.city,
    district: address.value.district,
    address: address.value.address,
    orderRemark: orderRemark.value,
    itemList: goodsList.value.map((item) => {
      return {
        goodsSkuId: item.skuId,
        goodsSkuName: item.skuName,
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        quantity: item.quantity,
        goodsImage: item.goodsImage,
        price: item.actualPrice
      }
    })
  }

  const orderId = await putOrderData(orderData)
  uni.showToast({title: '订单创建成功，发起支付中', icon: 'none'})
  uni.navigateTo({
    url: '/pages/order/pay?order_id=' + orderId
  })

}

const numberChange = async(ret) => {
  const item = goodsList.value[ret.index]
  item.quantity = ret.number
  item.actualPrice =  await getActrualPrice(item.skuId, item.quantity)

}

onLoad((params) => {
  if (params.sku_id) {
    skuId.value = params.sku_id
    listBySkuId()
  }
  if (params.cart_ids) {
    cartSkuIds.value = params.cart_ids
    listByCart()
  }
  getAddress()
})
</script>


<style lang="scss">
page {
  background: $page-color-base;
  padding-bottom: 100rpx;
}

.goods-area {
  .goods-item {
    display: flex;

    .disabled {
      color: #fff !important;
      width: 70%;
      height: 70%;
      background-color: rgba(51, 51, 51, 0.5);
    }

    image {
      flex-shrink: 0;
      display: block;
      width: 180rpx;
      height: 180rpx;
      border-radius: 4rpx;
    }
  }
}

.oper-area {
  z-index: 998;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);

  .submit {
    width: 280rpx;
    height: 100rpx;
  }
}

/* 优惠券区 */
.coupon-area {
  max-height: 60vh;
  overflow: auto;

  .coupon-item {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .content {
      &:after {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        height: 0;
        border-bottom: 1px dashed #f3f3f3;
        transform: scaleY(50%);
      }
    }

    .circle {
      position: absolute;
      bottom: -10rpx;
      z-index: 10;
      width: 20rpx;
      height: 20rpx;
      background: #f5f5f5;
      border-radius: 50%;

      &.r {
        right: -6rpx;
      }

      &.l {
        left: -6rpx;
      }
    }
  }
}

.coupon-none {
  width: 100%;
  height: 100%;
  line-height: 30vh;
  display: flex;
  align-items: center;
  justify-content: center;

  .coupon-none-tip {
    color: #909399;
  }
}
</style>
