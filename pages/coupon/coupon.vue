<template>
  <view class="container">

    <view class="pos-f w-full navbar-area bg-main">
      <view class="state-area dflex-a">
        <template v-for="(item, index) in navList" :key="index">
          <view :class="{ active: tabCurrentIndex === index }"
                class="nav-item dflex-c pos-r fs padding-lr-lg h-full"
                @click="tabCurrentIndex = index">
            {{ item.state }}
            <text v-if="item.cnt > 0">({{ item.cnt }})</text>
          </view>
        </template>
      </view>
    </view>

    <swiper :current="tabCurrentIndex" :disable-touch="true" class="swiper-box swiper-area h-full" duration="300"
            @change="changeTab">
      <swiper-item class="tab-content" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
        <scroll-view class="list-scroll-content h-full" scroll-y @scrolltolower="loadData()">
          <!-- 空白页 -->
          <use-empty v-if="tabItem.list.length === 0 " e-style="round" e-type="cart" tip="暂无优惠券"
                     btn-tip="去领券" height="93vh"></use-empty>
          <view class="padding-lr" @click="toGoodsList()" v-for="(item, index) in tabItem.list" :key="index">
            <view class="coupon_box border-radius margin-top-sm bg-main"
                  :class="{ 'disabled': tabItem.state !== '已领取' }">
              <view class="dflex-b">
                <view class="left pos-a h-full dflex-c dflex-flow-c">
                  <view >
                    <text class="price fs-big">{{ item.discountAmount }}</text>
                  </view>
                  <view class="fs-sm" v-if="item.minOrderAmount > 0">满{{ item.minOrderAmount }}元使用</view>
                  <view class="fs-sm" v-else>无限制</view>
                </view>
                <view class="right padding left_t flex1">
                  <view class="dflex-b padding-bottom-xs">
                    <view class="fwb fs">{{ item.name }}</view>
                  </view>
                  <view v-if="tabItem.state === '已使用'" class="ft-dark iconfont iconyishiyong"></view>
                  <view v-if="tabItem.state === '已过期'" class="ft-dark iconfont iconyiguoqi"></view>
                  <view class="dflex-b ft-dark fs-xs padding-bottom border-line">
                    <view class="">有效期至 {{ item.validEndTime }}</view>
                  </view>
                  <view class="fs-xs ft-dark padding-top-xs">{{ item.remark || '详细信息' }}</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 上拉加载更多 -->
          <use-loadmore v-if="tabItem.list.length > 0 && tabItem.loaded && tabItem.hasmore"
                        :type="tabItem.loadingType"></use-loadmore>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>
<script setup>
import {ref, watch} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getUserCouponListData} from "@/common/api/coupon";
// todo

const tabCurrentIndex = ref(0)
const navList = ref([
  {
    id: 1,
    state: '已领取',
    cnt: 0,
    loadingType: 'more',
    list: []
  },
  {
    id: 2,
    state: '已使用',
    cnt: 0,
    loadingType: 'more',
    list: []
  },
  {
    id: 3,
    state: '已过期',
    cnt: 0,
    loadingType: 'more',
    list: []
  }
])

const loadData = async () => {
  const currentNav = navList.value[tabCurrentIndex.value]
  currentNav.list = await getUserCouponListData({status: currentNav.id || 1})

}
const changeTab = (e) => {
  tabCurrentIndex.value = e.detail.current
}

const toGoodsList = () => {
  uni.navigateTo({
    url: '/pages/goods/goods-list'
  })
}
watch(() => tabCurrentIndex, () => {
  loadData()
})

onShow(() => {
  loadData()
})
</script>

<style lang="scss">
page,
.container {
  min-height: 100%;
  background: $page-color-base;
}

.container {
  padding-top: 7vh;
}

/* 优惠券状态区 */
.navbar-area {
  top: 0;
  white-space: nowrap;

  .state-area {
    height: 7vh;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
    z-index: 10;
  }

  .nav-item {
    flex: 1;

    &.active {
      &:after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        bottom: 0;
        width: 44px;
        height: 0;
        border-bottom: 2px solid $base-color;
      }
    }
  }
}

/* #ifdef H5 */
.navbar-area {
  margin-top: calc(44px + env(safe-area-inset-top));
}

/* #endif */

/* 优惠券轮播区 */
.swiper-area {
  height: 93vh;
}

.coupon_box {
  position: relative;

  &:last-child {
    margin-bottom: 20rpx;
  }

  .left {
    background-color: $base-color;
    color: #fff;
    width: 30%;

    .price {
      color: #fff !important;
    }
  }

  .right {
    margin-left: 30%;
  }

  .discount {
    font-weight: 580;
  }

  .discount::after {
    content: '折';
    font-size: 24rpx;
    margin-left: 6rpx;
  }

  .border-line {
    border-bottom: 1px dotted #ededed;
  }
}

.disabled {
  .left {
    background-color: #d9d9d9;
    color: #b2b2b2 !important;

    .price {
      color: #b2b2b2 !important;
    }
  }

  .iconfont {
    position: absolute;
    top: 0rpx;
    right: 30rpx;
    font-size: 110rpx;
  }
}
</style>
