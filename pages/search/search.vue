<template>
  <view class="use-page">

    <!-- 搜索内容区 -->
    <view class="search-area pos-r w-full padding-lr dflex-b">
      <view class="h-full flex1 dflex-c">
        <view class="icon-search pos-a">
          <text class="iconfont iconsousuo-01"></text>
        </view>
        <input type="text" class="pos-a padding-left padding-tb-xs border-radius-lg box-sizing-b" maxlength="20"
               placeholder="请输入关键字" @confirm="handleSearch()" v-model="keyword"/>
      </view>

      <view class="bg-base border-radius-big padding-tb-xs padding-lr margin-left" @click="handleSearch()">搜索</view>
    </view>
    <view class="gap"></view>

    <!-- 搜索历史区 -->
    <view class="padding-lr w-full padding-top-lg" v-if="historyList && historyList.length > 0">
      <view class="dflex-b">
        <view class="dflex">
          <text>搜索历史</text>
        </view>
        <view class="iconfont iconfont iconlajitong-01 dflex-c ft-dark padding-sm" @click="clearHistory"></view>
      </view>
      <view class="dflex dflex-wrap-w">
        <view
          class="item margin-right-sm margin-bottom-sm dflex bg-drak border-radius-lg padding-tb-xs padding-lr"
          v-for="(item,index) in historyList" :key="index" @click="handleSearch(item)">
          <text>{{ item }}</text>
        </view>
      </view>
    </view>
    <!-- 热门搜索区 -->
    <view class="padding-lr w-full padding-top-lg" v-if="hotList && hotList.length > 0">
      <view class="padding-bottom-sm dflex-b">
        <view class="dflex">
          <text>热门搜索</text>
        </view>
      </view>
      <view class="dflex dflex-wrap-w">
        <view
          class="item margin-right-sm margin-bottom-sm dflex bg-drak border-radius-lg padding-tb-xs padding-lr"
          v-for="(item, index) in hotList" :key="index" @click="handleSearch( item)">
          <text>{{ item }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {
  getHistoryListData,
  getHotSearchListData,
  postHistoryData,
  postHotSearchData,
  deleteHistoryData
} from "@/common/api/search"
import {useUserStore} from "@/stores/user"

const useUser = useUserStore()

const keyword = ref('')
const historyList = ref([])
const hotList = ref([])


const handleSearch = async (key) => {
  console.log(999,key)
  if (key) {
    keyword.value = key
  }
  console.log(555,useUser.isLogin)
  console.log(666,keyword.value)
  await addHotSearch(keyword.value)
  if (useUser.isLogin) {
    await addHistory(keyword.value)
  }
  uni.navigateTo({
    url: '/pages/goods/goods-list?keyword=' + keyword.value
  })
}
const listHistory = async () => {
  historyList.value = await getHistoryListData()
}
const listHotSearch = async () => {
  hotList.value = await getHotSearchListData()
}
const addHistory = async (key) => {
  await postHistoryData(key)
  await listHistory()
}
const addHotSearch = async (key) => {
  await postHotSearchData(key)
  await listHotSearch()
}
const clearHistory = async () => {
  await deleteHistoryData()
  await listHistory()
}
onLoad(() => {
  listHotSearch()
  if (useUser.isLogin) {
    listHistory()
  }
})
</script>

<style lang="scss">
.search-area {
  height: 120rpx;

  input {
    width: 560rpx;
    padding-left: 60rpx;
    height: 64rpx;
    line-height: 64rpx;
    background-color: #f5f5f5;
  }

  .icon-search {
    top: 50%;
    left: 40rpx;
    transform: translate(0, -50%);
    z-index: 1;

    text {
      color: #c0c0c0;
    }
  }
}
</style>
