<template>
  <view class="content bg-drak ft-main">
    <view class="gap"></view>
    <view class="row dflex border-line padding-lr">
      <text class="tit">收货人</text>
      <input class="input" type="text" v-model="addrData.consignee" placeholder="请输入收货人姓名"
             placeholder-class="placeholder"/>
    </view>
    <view class="row dflex border-line padding-lr">
      <text class="tit">手机号</text>
      <input class="input" type="number" v-model="addrData.telephone" placeholder="请输入手机号码"
             placeholder-class="placeholder"/>
    </view>


    <view class="row dflex border-line padding-left">
      <text class="tit">所在地区</text>
      <view class="flex1 ">
        <view class="dflex-b flex1 input_t" @click="openCityPicker">
          <text v-if="addrData.province">{{ addrData.province }}{{ addrData.city }}{{ addrData.district }}</text>
          <text v-else class="text-[gray] text-[0.9375rem]" >请选择</text>

        </view>
        <!-- <use-pickeraddr class="flex1" @change="changeAddr">
          <view class="input_t">{{ addressName }}</view>
        </use-pickeraddr> -->
      </view>
    </view>
    <view class="row dflex border-line padding-lr">
      <text class="tit">详细地址</text>
      <input class="input" type="text" v-model="addrData.address" placeholder="请输入详细地址"
             placeholder-class="placeholder"/>
    </view>
    <!--<view class="gap"></view>-->
    <!--<view class="row dflex-b padding-lr">-->
    <!--  <text class="tit">设为默认</text>-->
    <!--  <switch :checked="addrDefault" color="#FF6A6C" @change="switchChange"/>-->
    <!--</view>-->

    <view class="padding w-full margin-top">
      <view class="dflex-b border-radius-big">
        <view class="tac padding-tb-sm flex1 bg-base" @click="submit">提交</view>
      </view>
    </view>
    <use-address ref="useAddressPopup" @onConfirm="handleCityConfirm" cancelColor="#bbb" themeColor="#FF6A6C">
    </use-address>

  </view>
</template>


<script setup>
import {ref} from 'vue'
import {onShow, onLoad} from '@dcloudio/uni-app'
import {postAddressUpdateData, putAddressAddData, getAddressInfoData} from '@/common/api/address'

const useAddressPopup = ref(null)
const addrData = ref({
  id: 0,
  consignee: '',
  telephone: '',
  province: '',
  city: '',
  district: '',
  address: ''
})
const id = ref(0)

const getAddressInfo = async () => {
  addrData.value = await getAddressInfoData(id.value)
}

const submit = async () => {
  // 表单验证
  if(!addrData.value.consignee){
    uni.showToast({
      title: '请输入收货人姓名',
      icon: 'none'
    })
    return
  }
  if(!addrData.value.telephone){
    uni.showToast({
      title: '请输入手机号码',
      icon: 'none'
    })
    return
  }
  if(!/^1[3456789]\d{9}$/.test(addrData.value.telephone)){
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    })
    return
  }
  if(!addrData.value.province){
    uni.showToast({
      title: '请选择所在地区',
      icon: 'none'
    })
    return
  }


  if (!id.value) {
    await putAddressAddData(addrData.value)
  } else {
    await postAddressUpdateData(addrData.value)
  }
  uni.showToast({
    title: '保存成功',
    icon: 'success',
    duration: 1000,
    success: () => {
      uni.navigateBack()
    }
  })
}

const openCityPicker = () => {
  useAddressPopup.value.open()
}
const handleCityConfirm = (res) => {
  if(res.labelArr && res.labelArr.length > 2){
    addrData.value.province = res.labelArr[0]
    addrData.value.city = res.labelArr[1]
    addrData.value.district = res.labelArr[2]
  }
}

onLoad((options) => {
  if (options.id) {
    id.value = options.id
  }
})
onShow(() => {
  if (id.value) {
    getAddressInfo()
  }
})
</script>


<style lang="scss">
page {
  background: $page-color-base;
}

.row {
  background: #fff;
  position: relative;
  height: 110rpx;

  .tit {
    flex-shrink: 0;
    width: 150rpx;
  }

  .input {
    flex: 1;
    font-size: 30rpx;
    padding-left: 0;
  }

  .input_t {
    color: #333;
  }
}
</style>
