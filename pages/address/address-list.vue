<template>
  <view class="padding-lr padding-bottom-big margin-bottom ft-main bg-drak">
    <view class="bg-main padding-top padding-lr border-radius margin-top-sm" v-for="(item, index) in addressList"
          :key="index" @click="selectAddress(item)">
      <view class="w-full dflex-wrap-w border-line">
        <view class="fwb margin-bottom-xs desc">
          <text>{{ item.province }} {{ item.city }}{{ item.district }}{{ item.address }}</text>
        </view>
        <view class="margin-bottom-sm">
          <text>{{ item.consignee }}</text>
          <text class="margin-left">{{ item.telephone }}</text>
        </view>
      </view>
      <view class="dflex-b">
        <view v-if="item.defaultFlag === 1" class="dflex active">
          <text class="iconfont iconxuanzhongzhuangtai padding-tb-sm padding-right-sm"></text>
          <text> 默认地址</text>
        </view>
        <view v-else class="dflex ft-dark" @tap.stop="setDefault(item.id)">
          <text class="iconfont iconweixuanzhongzhuangtai padding-tb-sm padding-right-sm"></text>
          <text> 设为默认</text>
        </view>
        <view class="dflex">
          <view class="padding-tb-sm padding-right-sm" @tap.stop="updateAddress(item)">
            <text
              class="iconfont iconbianji-01 ft-dark"></text>
          </view>
          <view class="padding-tb-sm padding-left-sm" @tap.stop="removeAddress(item.id)">
            <text
              class="iconfont iconlajitong-01 ft-dark"></text>
          </view>
        </view>
      </view>
    </view>

    <view class="btn-container dflex-b pos-f border-radius-big">
      <view class="tac padding-tb-sm flex1 bg-base" @click="addAddress()">添加地址</view>
    </view>
  </view>
</template>


<script setup>
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {
  postAddressDefaultData,
  getAddressListData,
  deleteAddressDeleteData
} from "@/common/api/address";

const addressList = ref([])

const addAddress = () => {
  uni.navigateTo({
    url: '/pages/address/address-form'
  })
}
const updateAddress = (address) => {
  uni.navigateTo({
    url: '/pages/address/address-form?id=' + address.id
  })
}
const removeAddress = async (id) => {
  await deleteAddressDeleteData(id)
  await listAddress()
  uni.showToast({title: '删除成功', icon: 'success'})
}
const setDefault = async (id) => {
  await postAddressDefaultData(id)
  await listAddress()
  uni.showToast({title: '设置成功', icon: 'success'})
}
const listAddress = async () => {
  addressList.value = await getAddressListData()
}
const selectAddress = (address) => {
  uni.$emit("handleAddressSelect", address)
  uni.navigateBack()
}
onShow(() => {
  listAddress()
})
</script>


<style lang="scss">
page {
  background: $page-color-base;
}

.desc {
  font-size: $font-lg;
}

.btn-container {
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
}
</style>
