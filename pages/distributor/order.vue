<template>
  <view class="mingxi">
    <view class="ticheng" v-if="list.length > 0">
      <block v-for="(item,index) of list" :key="index">
        <li class="tc">
          <view class="tc_l">
            <span>分销提成-{{item.remark}}</span><br/>{{
              item.createTime
            }}
          </view>
          <view class="tc_2">
            + {{ item.commission }}元<br/>
            <span v-if="item.status===0">未结算</span>
            <span class="text-mint" v-if="item.status===1">已结算</span>
          </view>
        </li>
      </block>
    </view>
    <use-empty v-else></use-empty>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import {getDistributionOrderListData} from "@/common/api/distribution";

const jump_cash = () => {
  uni.navigateTo({
    url: '/pages/cash/cash'
  })
}

const list = ref([])
const getList = async () => {
  list.value = await getDistributionOrderListData()
}

onShow(() => {
  getList()
})
onLoad(() => {
})
</script>

<style lang="less">
.mingxi {
  .head {
    display: flex;
    margin: 10px 0;
  }

  .head_l {
    display: flex;
    width: 85%;
    justify-content: space-around;
  }

  .head_r {
    width: 15%;
    text-align: center;
  }

  .head_l_1 {
    border: 1px solid #F2F2F2;
    padding: 0px 15px;
    line-height: 25px;
  }

  .head_btn {
    margin: 10px 20px 0;
    border: 1px solid #F2F2F2;
    padding: 0px 15px;
    line-height: 25px;
  }

  .ling {
    color: #E1461D;
    border: 1px solid #E1461D;
    padding: 0px 15px;
    line-height: 25px;
  }

  .shouyi {
    border-top: 1px solid #EBEBEB;
    border-bottom: 1px solid #EBEBEB;
    background-color: #FAFAFA;
    display: flex;
    height: 30px;
    line-height: 30px;
    padding: 3px 10px;
    margin-top: 15px;
  }

  .sy_l {
    width: 50%;
  }

  .sy_l span {
    font-weight: bold;
  }

  .ticheng li:nth-of-type(odd) {
    background-color: #efefef;
  }

  .ticheng li:nth-of-type(even) {
    background-color: #fff;
  }

  .tc {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    line-height: 25px;
    font-size: 14px;
  }

  .tc_l {
    color: #9A9A9A;
  }

  .tc_l span {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }

  .tc_2 {
    color: #E1461D;
  }

  .tc_2 span {
    color: #9A9A9A;
  }
}
</style>
