<template>
  <view class="tixian">
    <view class="one items-center">
      <view class="t_tit">提现到</view>
      <view class="bg-mint text-white border-radius px-3 py-0.5 mr-5" @click="jump_card()">添加银行卡</view>
    </view>
    <view class='swip-box'>
      <swiper @change="changeCard" class="swiper">
        <swiper-item v-for="(item,index) of cardList" :key="index" style="height: 150px;">
          <view class="card bg-mint" @click="jump_card(item.id)">
            <view class="card_l">
            </view>
            <view class="card_r">
              <view class="card_r_1">{{ item.bankName }}</view>
              <view class="card_r_2">{{ item.accountName }}</view>
              <view class="card_r_3">{{ item.accountNo }}</view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="BH"></view>
    <view class="je">提现金额</view>
    <view class="hsuru">
      <view class="hu_l">
        <input v-model="form.amount" placeholder="0" class="px-4"
               placeholder-style="font-size: 24px;font-weight: bold;height:35px;"/>
      </view>
    </view>
    <view class="yue">
      <view class="yue_l">可提现余额 <span class="text-lg ">{{ total }}</span> 元</view>
      <view class="text-mint" @click="form.amount = total"><span>全部提现</span></view>
    </view>
    <view class="yue text-gray-500">提现规则：提现免手续费，提现到账时间1-3个工作日，请耐心等待。</view>
    <view class="yue"></view>
    <view class="qdtx bg-mint" @click="submit">确定提现</view>
  </view>
</template>


<script setup>
import {ref, reactive} from 'vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import {getWithdrawCardListData, getWithdrawMoneyAvailableData, putWithdrawApplyData} from "@/common/api/withdraw"

const total = ref(0)
const changeCard = (e) => {
  const idx = e.detail.current
  form.accountName = cardList.value[idx].accountName
  form.accountNo = cardList.value[idx].accountNo
  form.bankName = cardList.value[idx].bankName
}

const form = reactive({
  bankName: '',
  accountName: '',
  accountNo: '',
  amount: ''
})

const cardList = ref([])
const jump_card = (id) => {
  if (id) {
    uni.navigateTo({
      url: '/pages/cash/card?id=' + id
    })
  } else {
    uni.navigateTo({
      url: '/pages/cash/card'
    })
  }
}

const submit = async () => {
  if (!form.amount) {
    uni.showToast({
      title: '请输入正确的提现金额',
      icon: 'none'
    })
    return
  }
  if (form.amount > total.value) {
    uni.showToast({
      title: '提现金额不能大于可提现金额',
      icon: 'none'
    })
    return
  }
  await putWithdrawApplyData(form)
  uni.showModal({
    title: '提示',
    content: '提现申请成功，请耐心等待',
    success: (res) => {
      form.amount = ''
      getMoney()
    }
  })


}

const getMoney = async () => {
  total.value = await getWithdrawMoneyAvailableData()
}

const getCardList = async () => {
  cardList.value = await getWithdrawCardListData()
  if (cardList.value.length < 1) {
    uni.showModal({
      title: '提示',
      content: '请先添加银行卡',
      success: (res) => {
        if (res.confirm) {
          jump_card()
        }
      }
    })
  } else {
    form.accountNo = cardList.value[0].accountNo
    form.accountName = cardList.value[0].accountName
    form.bankName = cardList.value[0].bankName
  }
}
onShow(() => {
  getCardList()
  getMoney()
})
onLoad(() => {
})
</script>


<style lang="less">
.tixian {
  .BH {
    height: 10px;
    background-color: #F8F8F8;
    margin: 0 0 10px;
  }

  .je {
    padding: 10px;
    font-size: 16px;
    font-weight: bold;
  }

  .hsuru {
    display: flex;
    border-bottom: 1px solid #F0F0F0;
    margin: 10px 10px 12px;
    padding: 10px 10px 10px 0;
    justify-content: space-between;
  }

  .hu_l input {
    font-size: 24px;
    font-weight: bold;
  }

  .yue {
    margin: 0 10px 20px;
    display: flex;
    justify-content: space-between;
  }

  .qdtx {
    margin: 0 10px 20px;
    border-radius: 20px;
    color: #fff;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
  }

  .card {
    margin: 0 20px 10px;
    border-radius: 10px;
    padding: 20px 10px;
    color: #fff;
    display: flex;
    box-shadow: 0 5px 10px #428675;
  }

  .card_l {
    width: 20%;
    text-align: center;
  }

  .card_l img {
    width: 45px;
    height: 45px;
  }

  .card_r {
    width: 80%;

    .card_r_1 {
      font-size: 16px;
      padding-bottom: 10px;
    }

    .card_r_2 {
      font-size: 14px;
      padding-bottom: 20px;
    }

    .card_r_3 {
      font-size: 14px;
    }
  }

  .one {
    display: flex;
    justify-content: space-between;

    .t_tit {
      font-size: 20px;
      padding: 20px;
      font-weight: bold;
    }

  }

}
</style>
