<template>
  <view class="shop_login bg-mint h-24">
    <view class="shop_tit" v-if="!form.id">添加银行卡</view>
    <view class="shop_tit" v-else>修改银行卡</view>
    <view class="s_login">
      <view class="biao_01">
        <view class="biao_01_l">开户行：</view>
        <view class="biao_01_r">
          <input class="uni-input" v-model="form.bankName" placeholder="请输入银行"/>
        </view>
      </view>

      <view class="biao_01">
        <view class="biao_01_l">账户名：</view>
        <view class="biao_01_r">
          <input class="uni-input" v-model="form.accountName" placeholder="请输入账户姓名"/>
        </view>
      </view>
      <view class="biao_01">
        <view class="biao_01_l" style="white-space:nowrap;">银行卡号：</view>
        <view class="biao_01_r">
          <input class="uni-input" v-model="form.accountNo" placeholder="请输入银行卡号"/>
        </view>
      </view>

    </view>
    <view class="btn bg-mint" @click="add" v-if="!form.id">提交</view>
    <view class="add_btn" v-else>
      <view class="add_btn_l" @click="remove">删除</view>
      <view class="add_btn_r bg-mint" @click="update">修改</view>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {
  putWithdrawCardData,
  postWithdrawCardData,
  getWithdrawCardData,
  deleteWithdrawCardData
} from "@/common/api/withdraw"

const form = ref({
  id: '',
  accountName: '',
  accountNo: '',
  bankName: ''
})

const add = async () => {
  const data = {...form.value}
  if (!validate(data)) {
    return
  }
  await putWithdrawCardData(data)
  uni.showToast({title: "添加成功"})
  uni.navigateBack()
}

const update = async () => {
  const data = {...form.value}
  if (!validate(data)) {
    return
  }
  await postWithdrawCardData(data)
  uni.showToast({title: "修改成功"})
  uni.navigateBack()
}

const validate = (data) => {
  if (!data.accountName) {
    uni.showToast({
      title: '请输入账户名',
      icon: 'none'
    })
    return false
  }
  if (!data.accountNo) {
    uni.showToast({
      title: '请输入银行卡号',
      icon: 'none'
    })
    return false
  }
  if (!data.bankName) {
    uni.showToast({
      title: '请输入开户行',
      icon: 'none'
    })
    return false
  }
  return true
}

const remove = async () => {
  uni.showModal({
    title: '提示',
    content: '确定要删除吗？',
    success: async (res) => {
      if (res.confirm) {
        await deleteWithdrawCardData(form.value.id)
        uni.showToast({title: "删除成功"})
        uni.navigateBack()
      }
    }
  })
}

const getCard = async () => {
  form.value = await getWithdrawCardData(form.value.id)
}

onLoad((options) => {
  if (options.id) {
    form.value.id = options.id
    getCard()
  }

})
</script>


<style lang="less">
.shop_login {
  padding-bottom: 1px;

  .shop_tit {
    font-size: 22px;
    color: #fff;
    padding: 20px 20px;
  }

  .uni-input {
    background-color: #fff;
    padding-top: 5px;
  }

  .s_login {
    margin: 0 10px;
    border-radius: 5px;
    padding: 40px 10px;
    background-color: #fff;
  }

  .l_01 {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #FBFBFB;
    height: 40px;
    line-height: 40px;
  }

  .l_02 {
    border-bottom: 1px solid #FBFBFB;
    height: 40px;
    line-height: 40px;
    color: #C2C2C2;
  }

  .biao_01 {
    padding: 10px 10px 10px;
    border-bottom: 1px solid #EDEDED;
    line-height: 30px;
    display: flex;
  }

  .biao_01_l {
    width: 25%;
    text-align: right;
    padding-right: 15px;
  }

  .btn {
    color: #fff;
    margin: 10px 20px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
    position: fixed;
    left: 0;
    bottom: 20px;
    z-index: 99;
    width: 90%;
  }

  .add_btn {
    position: fixed;
    left: 0;
    bottom: 20px;
    z-index: 99;
    width: 90%;
    margin: 10px 20px;
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;

    .add_btn_l {
      width: 47%;
      background-color: #DBD6D6;
      text-align: center;
      border-radius: 20px;
    }

    .add_btn_r {
      width: 47%;
      color: #fff;
      text-align: center;
      border-radius: 20px;
    }
  }

}
</style>
