<template>
  <view class="success">
    <view class="s_tit">
      提现申请成功
    </view>
    <view class="BH10"></view>
    <view class="chengg">
      <icon type="success" color="#428675" size="100"></icon>
      <view class="cg">提现申请成功</view>
      <view class="cg_q">¥ {{ info.amount }}</view>
    </view>
    <view class="txfs">
      <view class="txfs_l">提现方式</view>
      <view class="txfs_r">{{ info.bankName }}（{{ info.accountName }}）({{info.accountNo}})
      </view>
    </view>
    <view class="txzt">提现状态</view>
    <column-steps class="ml-10" :options="steps"
               active-icon="checkbox"
               direction="column"
               :active="step" color="#428675">
    </column-steps>
    <view class="dzsj mt-5">
      <view class="dzsj_01">到账时间</view>
      <view class="dzsj_02">
        1、银行卡方式体现工作日15:00之前提现，2小时到账，15:00之后体现次日到账。<br/>
        2、节假日提现顺延。
      </view>
    </view>
    <view class="btn bg-mint border-radius-big" @click="jump()">确认</view>
  </view>
</template>

<script setup>
import {ref, reactive} from 'vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import {getWithdrawLogInfoData} from "@/common/api/withdraw";

const id = ref(0)
const info = ref({})
const steps = reactive([])
const step = ref(0)

const init = async () => {
  info.value = await getWithdrawLogInfoData(id.value)
  steps.push({title: '提现申请', desc: info.value.createTime})
  steps.push({title: '提现成功', desc: info.value.completeTime})
  if(info.value.status === 2){
    step.value = 1
  }
}
const jump = ()=>{
  uni.navigateBack()
}

onLoad((options) => {
  id.value = options.id
})
onShow(() => {
  init()
})

</script>


<style lang="less" scoped>
.success {
  .s_tit {
    text-align: center;
    padding: 10px;
    font-size: 16px;
    position: relative;
  }

  .s_tit span {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 14px;
  }

  .BH10 {
    height: 8px;
    background-color: #F2F2F2;
  }

  .chengg {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #FBFBFB;
  }

  .chengg img {
    width: 65px;
    height: 65px;
  }

  .cg {
    padding: 5px 0 15px;
  }

  .cg_q {
    font-size: 30px;
    font-weight: bold;
  }

  .txfs {
    padding: 0px 10px;
    border-bottom: 1px solid #FBFBFB;
    display: flex;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
  }

  .txfs_r {
    color: #9D9D9D;
  }

  .txfs_r img {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }

  .txzt {
    padding: 15px 10px 5px;
  }

  .dzsj {
    padding: 0 10px;
    color: #919191;
  }

  .dzsj_01 {
    color: #000;
    font-size: 14px;
    padding-bottom: 5px;
  }

  .btn {
    margin: 25px;
    color: #fff;
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
  }
}
</style>

