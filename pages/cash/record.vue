<template>
  <view class="record">
    <view class="head bg-mint">
      <view class="head_l">
        <view class="head_l_1">余额（元）</view>
        <view class="head_l_2">{{commission}}</view>
      </view>
      <view class="head_r text-mint" @click="jump_cash">提现</view>
    </view>
    <block v-for="(item,index) of list" :key="index">
      <view class="list" @click="toDetail(item.id)">
        <view class="list_01">
          <view class="list_01_l">提现</view>
          <view class="list_01_r">-{{ item.amount }}元</view>
        </view>
        <view class="list_02">
          <view class="list_01_l">{{ item.createTime }}</view>
          <view class="list_01_r text-mint" v-if="item.status===1">处理中</view>
          <view class="" v-else-if="item.status===2">提现成功</view>
          <view class="text-red-600" v-else>提现失败</view>
        </view>
      </view>
    </block>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getWithdrawListData, getWithdrawMoneyAvailableData} from "@/common/api/withdraw"

const list = ref([])
const commission = ref(0)

const init = async ()=>{
  list.value = await getWithdrawListData()
  commission.value = await getWithdrawMoneyAvailableData()
}

const jump_cash = () => {
  uni.navigateTo({
    url: '/pages/cash/cash'
  })
}

const toDetail = (id)=>{
  uni.navigateTo({
    url: '/pages/cash/detail?id=' + id
  })
}

onShow(() => {
  init()
})
</script>


<style lang="scss">
.record {
  font-size: 14px;

  .head {
    padding: 20px 10px;
    display: flex;
    justify-content: space-between;

    .head_l {
      color: #fff;

      .head_l_2 {
        font-size: 22px;
        padding-top: 10px;
      }
    }

    .head_r {
      background-color: #fff;
      height: 30px;
      line-height: 30px;
      width: 90px;
      text-align: center;
      border-radius: 20px;
      margin-top: 15px;
    }
  }

  .list {
    margin: 15px 10px;
    box-shadow: 0 0 10px #EDEDED;
    border-radius: 5px;
    padding: 15px 10px;

    .list_01 {
      display: flex;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 10px;
    }

    .list_02 {
      display: flex;
      justify-content: space-between;
      color: #9A9A9A;
    }
  }
}
</style>
