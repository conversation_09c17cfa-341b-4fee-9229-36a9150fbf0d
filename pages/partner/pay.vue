<template>
  <view class="app w-full">
    <view class="price-box dflex-c dflex-flow-c">
      <view>支付金额</view>
      <view class="price fs-xxxl margin-top-sm">{{ totalPrice }}</view>
    </view>

    <view class="pay-type-list">
      <view class="type-item dflex-b pos-r padding-tb-sm">
        <text class="iconfont iconweixin"></text>
        <view class="item flex1">
          <text class="tit">微信支付</text>
          <text>推荐使用</text>
        </view>
        <label class="radio">
          <radio value="" color="#FF6A6C" checked=""/>
        </label>
      </view>

    </view>

    <view class="padding w-full margin-top-big pos-a" style="bottom: 30rpx;">
      <view class="dflex-b border-radius-big">
        <view class="tac padding-tb-sm flex1 bg-base" :class="loading ? 'bg-disabled' : ''" @click="confirm">
          确认支付
        </view>
      </view>
    </view>
  </view>
</template>


<script setup>
import {ref, computed} from 'vue'
import {onShow, onLoad} from '@dcloudio/uni-app'
import {getPartnerApplyData} from "@/common/api/partner"
import {useUserStore} from "@/stores/user";
import {getAuthUrlData, getOpenidData, getPrePartnerApplyOrderData} from "@/common/api/wechat";

const userStore = useUserStore()
const openid = computed(() => {
  return userStore.getOpenid()
})
const loading = ref(false)
const code = ref('')
const apply = ref({})
const totalPrice = ref(0)

const getUrl = async () => {
  location.href = await getAuthUrlData({url: location.href})
}
const payH5 = (data) => {
  // #ifdef WEB
  function onBridgeReady () {
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest', data,
      function (res) {
        if (res.err_msg === "get_brand_wcpay_request:ok") {
          uni.showToast({title: '支付成功'})
          uni.redirectTo({
            url: '/pages/tabbar/user'
          })

        } else {
          uni.showToast({title: '支付失败', icon: 'error'})
        }
      });
  }

  if (typeof WeixinJSBridge == "undefined") {
    if (document.addEventListener) {
      document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
    } else if (document.attachEvent) {
      document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
      document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
    }
  } else {
    onBridgeReady();
  }
  // #endif
}

const confirm = async () => {
  const payStr = await getPrePartnerApplyOrderData({openid: openid.value, partner_apply_id: apply.value.id})
  payH5(JSON.parse(payStr))

}
const init = async () => {
  apply.value = await getPartnerApplyData()
}
onShow(() => {
  init()
})
const getOpenid = async () => {
  const openid = await getOpenidData(code.value)
  userStore.setOpenid(openid)
}
onLoad((options) => {
  if (!options.code && !openid.value) {
    getUrl()
  } else if (!openid.value) {
    code.value = options.code
    getOpenid()
  }
  if(options.totalPrice){
    totalPrice.value = options.totalPrice
  }
})
</script>


<style lang='scss'>
.price-box {
  height: 266rpx;
  font-size: 28rpx;
  color: #909399;
}

.pay-type-list {
  padding-left: 60rpx;
  padding-right: 60rpx;

  .type-item {
    height: 120rpx;
    font-size: 30rpx;
  }

  .iconfont {
    width: 100rpx;
    font-size: 52rpx;
  }

  .iconhuiyuan {
    color: #fe8e2e;
  }

  .iconweixin {
    color: #36cb59;
  }

  .iconalipay {
    color: #01aaef;
  }

  .iconqq {
    color: #13c6fe;
  }

  .iconbaidu {
    color: #306cff;
  }

  .icontoutiao {
    color: #f85959;
  }

  .tit {
    font-size: $font-lg;
    color: $font-color-dark;
    margin-bottom: 4rpx;
  }

  .item {
    display: flex;
    flex-direction: column;
    font-size: $font-sm;
    color: $font-color-light;
  }
}
</style>
