<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <view class="bg-white px-4 py-3 border-b border-gray-100">
      <text class="text-lg font-semibold text-gray-800">下级用户</text>
    </view>

    <!-- 统计信息 -->
    <view class="bg-white mx-4 mt-4 rounded-lg shadow-sm p-4">
      <view class="flex items-center justify-between">
        <view class="text-center">
          <text class="text-2xl font-bold text-mint">{{ list.length }}</text>
          <text class="block text-sm text-gray-500 mt-1">总人数</text>
        </view>
        <view class="w-px h-8 bg-gray-200"></view>
        <view class="text-center">
          <text class="text-2xl font-bold text-mint">{{ todayCount }}</text>
          <text class="block text-sm text-gray-500 mt-1">今日新增</text>
        </view>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="mx-4 mt-4">
      <!-- 空状态 -->
      <view v-if="list.length === 0" class="bg-white rounded-lg shadow-sm p-8 text-center">
        <view class="text-gray-400 text-6xl mb-4">👥</view>
        <text class="text-gray-500 text-base">暂无下级用户</text>
        <text class="block text-gray-400 text-sm mt-2">邀请好友加入，开始赚取收益</text>
      </view>

      <!-- 用户列表项 -->
      <view v-else class="space-y-3">
        <view
          v-for="(item, index) in list"
          :key="index"
          class="bg-white rounded-lg shadow-sm p-4 border border-gray-100 hover:shadow-md transition-shadow"
        >
          <view class="flex items-center justify-between">
            <!-- 左侧用户信息 -->
            <view class="flex items-center flex-1">
              <!-- 头像占位符 -->
              <view class="w-12 h-12 bg-mint-light rounded-full flex items-center justify-center mr-3">
                <text class="text-mint text-lg font-semibold">{{ getUserInitial(item.phone) }}</text>
              </view>

              <!-- 用户详情 -->
              <view class="flex-1">
                <view class="flex items-center mb-1">
                  <text class="text-base font-medium text-gray-800 mr-2">{{ formatPhone(item.phone) }}</text>
                  <view class="bg-mint-light px-2 py-0.5 rounded-full">
                    <text class="text-mint text-xs">下级</text>
                  </view>
                </view>
                <view class="flex items-center text-sm text-gray-500">
                  <text class="mr-4">加入时间: {{ formatDate(item.createTime) }}</text>
                  <text>{{ getJoinDays(item.createTime) }}天前</text>
                </view>
              </view>
            </view>

            <!-- 右侧状态指示 -->
            <view class="flex flex-col items-end">
              <view class="w-2 h-2 bg-green-400 rounded-full mb-1"></view>
              <text class="text-xs text-gray-400">活跃</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view v-if="list.length > 0" class="text-center py-6">
      <text class="text-gray-400 text-sm">已显示全部 {{ list.length }} 位下级用户</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerSubordinateListData} from "@/common/api/partner"

interface Item {
  phone: string;
  createTime: string;
}

const list = ref<Item[]>([])

// 计算今日新增用户数量
const todayCount = computed(() => {
  const today = new Date().toDateString()
  return list.value.filter(item => {
    const itemDate = new Date(item.createTime).toDateString()
    return itemDate === today
  }).length
})

// 格式化手机号显示（中间4位用*代替）
const formatPhone = (phone: string): string => {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取用户名首字母（基于手机号后两位）
const getUserInitial = (phone: string): string => {
  if (!phone) return 'U'
  const lastTwo = phone.slice(-2)
  return String.fromCharCode(65 + (parseInt(lastTwo) % 26))
}

// 格式化日期显示
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算加入天数
const getJoinDays = (dateStr: string): number => {
  const joinDate = new Date(dateStr)
  const today = new Date()
  const diffTime = Math.abs(today.getTime() - joinDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

const init = async () => {
  try {
    list.value = await getPartnerSubordinateListData()
  } catch (error) {
    console.error('获取下级用户列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})
</script>


<style scoped lang="scss">

</style>
