<template>
  <view>

  </view>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerSubordinateListData} from "@/common/api/partner"
interface Item {
  phone: string;
  createTime: string;
}
const list = ref<Item[]>([])

const init = async () => {
  list.value = await getPartnerSubordinateListData()
}

onShow(() => {
  init()
})


</script>


<style scoped lang="scss">

</style>
