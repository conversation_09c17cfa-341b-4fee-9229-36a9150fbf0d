<template>
  <view class="reseller">
    <view class="top">
      <view class="num" style="margin-left: 30px;">
        <view>累计收益</view>
        <view class="txc">{{incomeTotal}}</view>
      </view>
      <view class="num1" style="">
        <view>当前提成</view>
        <view style="font-size: 40px;">{{commission}}</view>
      </view>
      <view class="num" style="margin-right: 30px;">
        <view>累计已提</view>
        <view>{{withdrawTotal}}</view>
      </view>
      <view class="tixian" @click="jump_cash">
        <view class="btn bg-mint" style="font-size: 16px;">立即提现</view>
      </view>
      <view class="jilu">
        <navigator url="/pages/cash/record">
          <view class="jl">提现记录 ></view>
        </navigator>
      </view>
    </view>

    <view class="icon">
      <view class="ico">
        <navigator url="/pages/partner/subordinate">
          <view class="tubiao">
            <image class="img" src="/static/images/subordinate.png"></image>
          </view>
          <view class="tt">
            <view class="text">下级用户</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/commission">
          <view class="tubiao">
            <image class="img" src="/static/images/pie.png"></image>
          </view>
          <view class="tt">
            <view class="text">订单提成</view>
          </view>
        </navigator>
      </view>

      <view class="ico">
        <navigator url="/pages/partner/monthly">
          <view class="tubiao">
            <image class="img" src="/static/images/monthly.png"></image>
          </view>
          <view class="tt">
            <view class="text">月度奖励</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/annual">
          <view class="tubiao">
            <image class="img" src="/static/images/annual.png"></image>
          </view>
          <view class="tt">
            <view class="text">年度奖励</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/deposit">
          <view class="tubiao">
            <image class="img" src="/static/images/deposit.png"></image>
          </view>
          <view class="tt">
            <view class="text">押金记录</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/cash/record">
          <view class="tubiao">
            <image class="img" src="/static/images/money.png"></image>
          </view>
          <view class="tt">
            <view class="text">提现记录</view>
          </view>
        </navigator>
      </view>
    </view>
    <view class="kong"></view>
  </view>
</template>


<script setup>
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getWithdrawMoneyAvailableData, getWithdrawIncomeTotalData, getWithdrawTotalData} from "@/common/api/withdraw"
import {useWxJs} from "@/common/utils";

const commission = ref(0)
const incomeTotal = ref(0)
const withdrawTotal = ref(0)

const {share} = useWxJs()


const jump_cash = () => {
  uni.navigateTo({
    url: '/pages/cash/cash'
  })
}

const init = async () => {
  commission.value = await getWithdrawMoneyAvailableData()
  incomeTotal.value = await getWithdrawIncomeTotalData()
  withdrawTotal.value = await getWithdrawTotalData()
}

onShow(() => {
  init()
  share()
})
</script>


<style lang="less">
page {
  background-color: #F5F5F5;
}

.reseller {
  background-color: #F5F5F5;
  min-height: 100vh;
  font-size: 14px;

  .top {
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 250px;
    background-color: #864275;

    .title {
      background-color: #FFFFFF
    }

    .num {
      font-size: 15px;
      line-height: 25px;
      text-align: center;
      margin-top: 33%;
      width: 33%;

    }

    .num1 {
      margin-top: 10%;
      text-align: center;
      width: 33%;
    }

  }

  .jilu {
    font-size: 15px;
    position: absolute;
    left: 78%;
    top: 20%;
  }

  .tixian {
    display: flex;
    justify-content: center;
    background-color: #F5F5F5;
    width: 55%;
    height: 60px;
    border-radius: 60px;
    position: absolute;
    bottom: -30px;
    left: 23%;

    .btn {
      margin-top: 7%;
      text-align: center;
      width: 85%;
      height: 40px;
      border: none;
      border-radius: 50px;
      line-height: 40px;
      color: #FFFFFF;
    }


  }

  .icon {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    margin-left: 3%;
    margin-right: 3%;
    margin-top: 10%;

    .ico {
      border-radius: 10px;
      width: 49%;
      height: 100px;
      margin-top: 10px;
      display: flex;
      border: none;
      font-size: 15px;
      flex-direction: column;
      background-color: #FFFFFF;

      .img {
        width: 32px;
        height: 32px;
      }

      .tubiao {
        margin-top: 20px;
        text-align: center;
      }

      .text {
        color: #8F8F94;
        padding-top: 5px;
        text-align: center;

      }
    }
  }

  .kong {
    height: 100px;
  }
}
</style>
