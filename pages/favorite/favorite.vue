<template>
  <view class="bg-drak" :class="[list.length <1 ? '' : 'padding-tb']">
    <!-- 空白页 -->
    <use-empty v-if="list.length <1" e-style="round" tip="无收藏数据"></use-empty>

    <view v-else class="padding-lr" v-for="(item, index) in list" :key="index">
      <view class="product border-radius-sm padding margin-bottom-sm bg-main" style="padding-bottom: 15rpx;">
        <view class="left" @click="toGoods(item.goods.id)">
          <image :src="item.goods.image" mode="aspectFill"></image>
        </view>
        <view class="margin-left-sm pos-r w-full">
          <text class="clamp-2" @click="toGoods(item.goods.id)">{{ item.goods.name }}</text>
          <view class="pos-a dflex-b price-box w-full">
            <text class="price padding-tb-sm" @click="toGoods(item.goods.id)">{{ item.goods.actualPrice }}</text>
            <view class="dflex-c ft-dark">
              <button class="btn no-border padding-0 fs-sm ft-dark" open-type="share" :id="item.goods.id">
                <view class="dflex-c fs-xs padding-tb-sm">
                  <text class="iconfont iconfenxiang margin-left-xs"></text>
                </view>
              </button>
              <view @tap.stop="deleteCollect(item.id)" class="dflex-c margin-left-sm padding-tb-sm">
                <text class="iconfont iconlajitong-01 margin-left-xs"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 上拉加载更多 -->
    <!--<use-loadmore v-if="!empty && hasmore" :type="loadmoreType"></use-loadmore>-->
    <!-- 置顶 -->
    <use-totop ref="usetop" bottom="150"></use-totop>

    <view v-if="list.length >0" class="fixed-top" @click="clear">
      <text class="iconfont iconlajitong-01 fs-xl"></text>
    </view>
  </view>
</template>


<script setup>
import {ref} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {deleteFavoriteAllData, deleteFavoriteData, getFavoriteListData} from "@/common/api/favorite";

const list = ref([])


const toGoods = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
const deleteCollect = async (id) => {
  await deleteFavoriteData(id)
  uni.showToast({
    title: '取消收藏成功'
  })
  await getList()

}
const clear = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空收藏吗？',
    success: (res) => {
      if (res.confirm) {
        deleteAll()
      }
    }
  })
}

const deleteAll = async () => {
  await deleteFavoriteAllData()
  uni.showToast({
    title: '清空成功'
  })
  await getList()
}

const getList = async () => {
  list.value = await getFavoriteListData()
}
onLoad(() => {
  getList()
})
</script>


<style lang="scss">
page {
  background: $page-color-base;
}

.product {
  display: flex;

  .left {
    image {
      width: 180rpx;
      height: 180rpx;
    }
  }

  .price-box {
    bottom: -20rpx;
  }
}
</style>
