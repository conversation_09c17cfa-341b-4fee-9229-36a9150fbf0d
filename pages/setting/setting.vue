<template>
  <view class="container">
    <use-list-title title="个人资料" iconfont="" @click="toProfile"></use-list-title>
    <view class="gap"></view>

    <use-list-title title="关于优选" iconfont=""></use-list-title>
    <view class="gap"></view>

    <view class="use-item">
      <button class="no-border wh-full tal" open-type="feedback" @click="toFeedback">意见反馈</button>
    </view>
    <view class="gap"></view>

    <use-list-title title="未来优选" iconfont=" " tip="版本 v1.0.250318"></use-list-title>
    <view class="gap"></view>

    <view class="use-item log-out-btn tac" @click="userStore.showLogin()">
      <text>切换账号</text>
    </view>
    <view class="gap"></view>

    <view class="use-item log-out-btn tac" @click="handleLogout">
      <text>退出登录</text>
    </view>

    <use-login-form ref="loginPopup"></use-login-form>
  </view>
</template>

<script setup>
import {useUserStore} from "@/stores/user"
import {deleteLogoutData} from "@/common/api"

const userStore = useUserStore()

const toProfile = () => {
  uni.navigateTo({
    url: '/pages/setting/profile'
  })
}
const handleLogout = async () => {
  await deleteLogoutData()
  userStore.logout()
  uni.showToast({title: '退出成功'})
  uni.switchTab({
    url: '/pages/tabbar/home'
  })
}
</script>

<style lang='scss'>
page {
  background: $page-color-base;
}

.use-item {
  height: 100rpx;
  line-height: 100rpx;
  position: relative;
  background: #fff;

  switch {
    transform: translateX(16rpx) scale(.84);
  }

  button {
    line-height: 100rpx;
    background: #fff;
    font-size: 15px;
  }
}
</style>
