<template>
  <view class="container">
    <view class="dflex-c pos-r margin-bottom margin-top">
      <view class="portrait-box">
        <image class="border-radius-c" :src="member.avatar || '/static/images/logo-mini.png'">
        </image>
      </view>
      <view class="margin-left-sm">
        <view>
          <text class="username">{{ member.nickname || '优选用户' }}</text>
        </view>
        <view v-if="member.phone">
          <text>{{ member.phone }}</text>
        </view>
      </view>
    </view>
    <!--表单-->
    <view class="use-item padding-lr dflex-b">
      <text class="tac">昵称:</text>
      <input class="flex-auto px-5" type="text" maxlength="15" v-model="form.nickname" placeholder="必填">
    </view>
    <view class="gap"></view>
    <view class="use-item padding-lr dflex-b">
      <text class="tac">邮箱:</text>
      <input class="flex-auto px-5" type="text" v-model="form.email" placeholder="可选">
    </view>
    <view class="gap"></view>
    <view class="use-item padding-lr dflex-b">
      <text class="tac">姓名:</text>
      <input class="flex-auto px-5" type="text" v-model="form.name" placeholder="可选">
    </view>

    <view class="dflex-b border-radius-big mt-10 mx-4">
      <view class="tac padding-tb-sm flex1 bg-base" @click="submit">提交</view>
    </view>
  </view>
</template>

<script setup>
import {ref, reactive} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getUserInfo, updateUserProfile} from "@/common/api"

const member = ref({})
const form = reactive({
  nickname: '',
  email: '',
  name: '',
  phone: '',
  avatar: '',
  username: ''
})

const init = async () => {
  const res = await getUserInfo()
  member.value = res.sysUser
  form.nickname = res.sysUser.nickname
  form.email = res.sysUser.email
  form.name = res.sysUser.name
  form.phone = res.sysUser.phone
  form.avatar = res.sysUser.avatar
  form.username = res.sysUser.username
}

const submit = async () => {
  // 校验邮箱格式
  if (form.email && !/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(form.email)) {
    uni.showToast({
      title: '邮箱格式不正确',
      icon: 'none'
    })
    return
  }
  // 昵称不能为空
  if (!form.nickname) {
    uni.showToast({
      title: '昵称不能为空',
      icon: 'none'
    })
    return
  }
  // 昵称长度15个字符
  if (form.nickname.length > 15) {
    uni.showToast({
      title: '昵称长度不能超过15个字符',
      icon: 'none'
    })
    return
  }
  // 姓名长度不能超过15个字符
  if (form.name && form.name.length > 15) {
    uni.showToast({
      title: '姓名长度不能超过15个字符',
      icon: 'none'
    })
    return
  }

  await updateUserProfile(form)
  uni.showToast({
    title: '修改成功'
  })
}

onLoad(() => {
  init()
})
</script>


<style lang='scss'>
page {
  background: $page-color-base;
}

image {
  width: 130rpx;
  height: 130rpx;
}

.use-item {
  height: 100rpx;
  line-height: 100rpx;
  position: relative;
  background: #fff;
}
</style>
