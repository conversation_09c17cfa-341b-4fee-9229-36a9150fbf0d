<template>
  <view class="bg-drak" :class="[list.length<1 ? '' : 'padding-tb']">
    <!-- 空白页 -->
    <use-empty v-if="list.length<1" e-style="round" tip="无足迹数据"></use-empty>

    <view v-else class="padding-lr" v-for="(item, index) in list" :key="index" @click="toGoods(item.id)">
      <view class="product border-radius-sm padding margin-bottom-sm bg-main" style="padding-bottom: 15rpx;">
        <view class="left">
          <image :src="item.img" mode="aspectFill"></image>
        </view>
        <view class="margin-left-sm pos-r w-full">
          <text class="clamp-2">{{ item.name }}</text>
          <view class="pos-a dflex-b price-box w-full">
            <text class="price">{{ item.actualPrice }}</text>
            <view class="dflex-c ft-dark">
              <view class="dflex-c fs-xs">
                <text class="margin-xs">浏览</text>
                <text>{{ item.visit_cnt }}</text>
              </view>
              <view @tap.stop="deleteBrowsing(item._id)" class="dflex-c margin-left-sm">
                <text
                  class="iconfont iconlajitong-01 margin-left-xs"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 上拉加载更多 -->
    <use-loadmore v-if="!empty && hasmore" :type="loadmoreType"></use-loadmore>
    <!-- 置顶 -->
    <use-totop ref="usetop" bottom="150"></use-totop>

    <view v-if="!empty" class="fixed-top" @click="clear">
      <text class="iconfont iconlajitong-01"></text>
    </view>
  </view>
</template>

<script setup>
//todo
</script>


<style lang="scss">
page {
  background: $page-color-base;
}

.product {
  display: flex;

  .left {
    image {
      width: 180rpx;
      height: 180rpx;
    }
  }

  .price-box {
    bottom: 0;
  }
}
</style>
