<template>
  <view :class="list.length>0 ? 'padding-top-big' : '' ">

    <!-- 空白页 -->
    <use-empty v-if="list.length===0" e-style="round" e-type="search" tip="搜索数据为空" btn-tip="重新搜索"
               height="70vh" :auto="false"
               @goto="toSearch"></use-empty>

    <!-- 列表区 -->
    <view v-else>
      <!-- 筛选区 -->
      <view class="navbar pos-f w-full dflex bg-main" :style="{ position: headerPosition }">
        <view class="nav-item dflex-c flex1 pos-r h-full" :class="{active: filterIndex === 0}"
              @click="navbarClick(0)">
          综合排序
        </view>
        <view class="nav-item dflex-c flex1 pos-r h-full" :class="{active: filterIndex === 1}"
              @click="navbarClick(1)">
          销量优先
        </view>
        <view class="nav-item dflex-c flex1 pos-r h-full" :class="{active: filterIndex === 2}"
              @click="navbarClick(2)">
          <text>价格</text>
          <view class="">
            <view class="iconfont iconjiantou02 ft-dark dflex-c"
                  :class="{active: priceOrder === 1 && filterIndex === 2}"></view>
            <view class="iconfont iconjiantou ft-dark dflex-c"
                  :class="{active: priceOrder === 2 && filterIndex === 2}"></view>
          </view>
        </view>
      </view>

      <!-- 商品列表区 -->
      <view class="goods-list">
        <view class="list dflex-b dflex dflex-wrap-w w-full">
          <view class="item bg-main border-radius-sm padding-bottom-sm" v-for="(item, index) in list"
                :key="index" @click="toGoods(item.id)">
            <view class="image-wrapper">
              <image mode="aspectFill" :lazy-load="true" :src="item.image"></image>
            </view>
            <text class="title clamp padding-sm">{{ item.name }}</text>
            <view class="padding-left-sm dflex-b">
              <text class="price">{{ item.actualPrice }}</text>
              <text class="ft-dark margin-right-sm fs-xs">已售 {{ item.sales }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 上拉加载更多 -->
      <use-loadmore :type="loadType"></use-loadmore>
    </view>

    <!-- 置顶 -->
    <use-totop ref="useTop"></use-totop>

    <!-- 03. 猜你想要 -->
    <use-hot-goods v-if="list.length === 0" title-type="round" title="猜你想要"></use-hot-goods>
  </view>
</template>

<script setup>
import {ref, watch} from 'vue'
import {onLoad, onPageScroll} from '@dcloudio/uni-app'
import {getGoodsListData} from "@/common/api/goods";
import {useWxJs} from "@/common/utils";
const {share} = useWxJs()


const list = ref([])
const loadType = ref('nomore')
const useTop = ref(null)
const headerPosition = ref('fixed')
const filterIndex = ref(0)
const priceOrder = ref(1)
const params = ref(null)
watch(() => params, () => {
  listGoods()
}, {deep: true})


const navbarClick = (index) => {
  if (filterIndex.value === index && index !== 2) {
    return;
  }
  filterIndex.value = index;
  if (index === 2) {
    priceOrder.value = priceOrder.value === 1 ? 2 : 1;
  } else {
    priceOrder.value = 0;
  }

  if (filterIndex.value === 0) {
    // 综合排序
    params.value.by = 'sort';
    params.value.sort = 'asc';
  } else if (filterIndex.value === 1) {
    // 销量优先
    params.value.by = 'sales';
    params.value.sort = 'desc';
  } else if (filterIndex.value === 2) {
    // 价格排序
    params.value.by = 'price';
    if (priceOrder.value === 1) {
      // 降序
      params.value.sort = 'desc';
    } else if (priceOrder.value === 2) {
      // 升序
      params.value.sort = 'asc';
    }
  }
  uni.pageScrollTo({
    duration: 300,
    scrollTop: 0
  })
}
const toGoods = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
const toSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  })
}
const listGoods = async () => {
  list.value = await getGoodsListData(params.value);
}
onPageScroll((e) => {
  // 兼容iOS端下拉时顶部漂移
  if (e.scrollTop >= 0) {
    headerPosition.value = "fixed";
  } else {
    headerPosition.value = "absolute";
  }
  // this.scrollTop = e.scrollTop
  useTop.change(e.scrollTop);
})
onLoad((options) => {
  params.value = {...options}
  share()
})
</script>


<style lang="scss">
page {
  background-color: $page-color-base;
}

.navbar {
  top: var(--window-top);
  left: 0;
  height: 100rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, .06);
  z-index: 10;

  .nav-item {
    color: $font-color-dark;
    font-size: 30rpx;

    &.active {
      &:after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 120rpx;
        height: 0;
        border-bottom: 4rpx solid $base-color;
      }
    }
  }

  .iconfont {
    width: 30rpx;
    height: 14rpx;
    font-size: 20rpx;
    line-height: 1;
    margin-left: 4rpx;
  }
}

.goods-list {
  .list {
    padding: 0 3vw 20rpx;
  }

  .item {
    width: 46vw;
    overflow: hidden;
    margin-top: 2vw;

    &:nth-child(2n) {
      margin-left: 1vw;
    }

    &:nth-child(2n + 1) {
      margin-right: 1vw;
    }
  }

  .image-wrapper {
    width: 100%;
    height: 300rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
      opacity: 1;
    }
  }
}
</style>
