// vite.config.js
import {defineConfig} from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
// import VueDevTools from 'vite-plugin-vue-devtools'
// import tailwindcss from 'tailwindcss'
// import autoprefixer from 'autoprefixer'
// import {join} from 'node:path'
// import tailwindcss from '@tailwindcss/vite'



/** ==== 处理 tailwind cli 的自动启动和打包 ==== */
const child_process = require('child_process')
let tailwindMode = process.env.NODE_ENV

// 主进程输出
console.log(`[tailwindcss] 开始${tailwindMode === 'production' ? '生产环境打包' : '开发模式监听'}`);
child_process.exec(
	// 这里指令对应 package.json 中的 npm scripts
	tailwindMode === 'production'
		? 'npm run tailwind-build'
		: 'npm run tailwind-dev',
	{
		cwd: __dirname, // 切换目录到当前项目，必须
		// 为子进程设置node的环境变量，否则无法执行npm命令
		env: {
			...process.env,
			PATH: process.env.PATH + (process.platform === 'win32' ? ';' : ':') + '/usr/local/bin:/opt/homebrew/bin:/Users/<USER>/Library/Caches/fnm_multishells/63477_1755763272594/bin'
		}
	},
	(error, stdout, stderr) => {
		// tailwind --watch 是一个持久进程，不会立即执行回调
		// process.stdout.write('tailwind success')
		if (error) {
			console.error('[tailwindcss] 异常，请检查');
			console.error(error);
			console.error(stdout);
			console.error(stderr);
		}
		if (tailwindMode === 'production') {
			console.log('[tailwindcss] 生产环境打包完成');
		}
	})



export default defineConfig({
	plugins: [
		uni(),
		// VueDevTools()
	],
	server: {
		port: 10081,
		proxy: {
			"/api": {
				target: "http://127.0.0.1:9999",
				ws: true, // 是否启用 WebSocket
				changeOrigin: true, // 是否修改请求头中的 Origin 字段
				rewrite: (path) => path.replace(/^\/api/, ''),
			}
		}
	}
});
