import http from "@/common/http"

export const getBannerListData = () => {
	return http.get('mall/client/banner/list', {header: {skipToken: '1'}})
}
export const getCategoryListData = () => {
	return http.get('mall/client/category/list', {header: {skipToken: '1'}})
}
// 注册
export const register = (data) => {
	return http.post('admin/register/user', {
		username: data.phone,
		password: data.password,
		phone: data.phone
	}, {header: {skipToken: '1'}})
}

// 登出
export const deleteLogoutData = () => {
	return http.delete('/auth/token/logout')
}

export const getUserInfo = () => {
	return http.get('/admin/user/info')
}

export const sendSms = (mobile) => {
	return http.get('/mall/client/user/smsCode' , { params: {mobile}, header: {skipToken: '1'}})
}

export const updateUserProfile = (data) => {
	return http.put('/admin/user/personal/edit', data)
}

export function getUploadUrl(key){
	return http.get(`admin/sys-file/upload-url`, {params: {key}})
}

// oss使用签名url上传，只支持put方式，并且要用原生fetch，避免其他工具封装了太多额外信息
export function upload(url, data){
	return fetch(url, {method: 'PUT', body: data})
}
