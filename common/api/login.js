import CONFIG from "@/common/config"
import {encryptionBase64, encryption} from '@/common/utils'
import Request from "luch-request";
import { useUserStore } from '@/stores/user'

const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';
const basicAuth = 'Basic ' + encryptionBase64(CONFIG.clientID);

/*用户登录采用单独的Request实例*/
const http = new Request()
http.setConfig(config => {
	config.baseURL = CONFIG.baseHttpUrl
	config.header = {
		...config.header,
		skipToken: true,
		Authorization: basicAuth,
		'Content-Type': FORM_CONTENT_TYPE,
	}
	return config
})

// 密码登录及后续逻辑
export const loginByPassword = async (data) => {
	const {username, password} = data;
	try {
		uni.showLoading()
		const res = await http.request({
			url: 'auth/oauth2/token',
			params: {username, scope: 'server', grant_type: 'password'},
			method: 'POST',
			data: {password: encryption(password, CONFIG.passwordEncKey)}
		});
		uni.hideLoading()
		if (res && res.statusCode === 200) {
			const user = useUserStore()
			uni.setStorageSync('userToken', res.data.access_token)
			uni.setStorageSync('refreshToken', res.data.refresh_token)
			user.setToken()
		}
		
		return res; // 返回结果以便外部使用
	} catch (error) {
		if (error.data) {
			uni.showToast({
				title: error.data.msg,
				icon: 'none'
			});
		}
		
		console.error('Login failed:', error);
		throw error; // 抛出错误以便外部处理
	} finally {
	
	}
}
// 短信登录及后续逻辑
export const loginBySMS = async (data) => {
	const {mobile, code} = data;
	try {
		uni.showLoading()
		const res = await http.request({
			url: 'auth/oauth2/token',
			params: {mobile: 'SMS@' + mobile, code: code, grant_type: 'mobile', scope: 'server'},
			method: 'POST'
		});
		uni.hideLoading()
		if (res && res.statusCode === 200) {
			const user = useUserStore()
			uni.setStorageSync('userToken', res.data.access_token)
			uni.setStorageSync('refreshToken', res.data.refresh_token)
			user.setToken()
		}
		
		return res; // 返回结果以便外部使用
	} catch (error) {
		if (error.data) {
			uni.showToast({
				title: error.data.msg,
				icon: 'none'
			});
		}
		
		console.error('Login failed:', error);
		throw error; // 抛出错误以便外部处理
	} finally {
	
	}
}
