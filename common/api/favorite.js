import http from "@/common/http";

export const getIsFavoriteData = (goods_id) => {
	return http.get('mall/client/favorite/is_favorite', {params: {goods_id}})
}

export const postFavoriteToggleData = (goods_id) => {
	return http.post('mall/client/favorite/toggle/' + goods_id)
}
export const getFavoriteListData = () => {
	return http.get('mall/client/favorite/list')
}

export const deleteFavoriteData = (id) => {
	return http.delete('mall/client/favorite/delete/' + id)
}

export const deleteFavoriteAllData = () => {
	return http.delete('mall/client/favorite/clear')
}

export const getFavoriteCountData = () => {
	return http.get('mall/client/favorite/count')
}
