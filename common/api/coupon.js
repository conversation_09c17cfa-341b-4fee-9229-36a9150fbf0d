import http from "@/common/http";

export const getUserCouponCountData = () => {
	return http.get('/mall/client/coupon/user_count')
}

export function getGoodsCouponListData (goodsId) {
	return http.get('/mall/client/coupon/goods_coupon', {params: {goodsId}})
}
export function putGetCouponData (id) {
	return http.put('/mall/client/coupon/get_coupon/' + id)
}

export function getUserCouponListData (params) {
	return http.get('/mall/client/coupon/user_coupon_list', {params})
}
