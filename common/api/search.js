import http from "@/common/http";

export const postHistoryData = (keyword) => {
	return http.post('/mall/client/search/history_add/' + keyword)
}

export const getHistoryListData = () => {
	return http.get('/mall/client/search/history_list')
}


export const deleteHistoryData = () => {
	return http.delete('/mall/client/search/history_clear/')
}


export const getHotSearchListData = () => {
	return http.get('/mall/client/search/hot_list', {header: {skipToken: '1'}})
}

export const postHotSearchData = (keyword) => {
	return http.post('/mall/client/search/hot_add', null, {params: {keyword}, header: {skipToken: '1'}})
}
