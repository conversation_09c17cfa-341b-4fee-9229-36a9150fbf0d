import http from "@/common/http";

/**
 * 添加购物车
 * @param data
 */
export const putCartAddData = (data) => {
	return http.put('/mall/client/cart/add', data)
}

export const postCartUpdateData = (data) => {
	return http.post('/mall/client/cart/update', data)
}

export const getCartListData = () => {
	return http.get('/mall/client/cart/list')
}

export const deleteAllCartData = () => {
	return http.delete('/mall/client/cart/clear')
}

export const getCartListBySkuIdsData = (sku_ids) => {
	return http.get('/mall/client/cart/list_by_sku_ids/' ,{params: {sku_ids}})
}
