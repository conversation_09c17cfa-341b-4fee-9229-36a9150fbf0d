import http from "@/common/http"

export const getAddressListData = () => {
	return http.get('/mall/client/address/list')
}

export const putAddressAddData = (data) => {
	return http.put('/mall/client/address/add', data)
}

export const postAddressUpdateData = (data) => {
	return http.post('/mall/client/address/update', data)
}

export const deleteAddressDeleteData = (id) => {
	return http.delete('/mall/client/address/delete/' + id)
}

export const  postAddressDefaultData = (id) => {
	return http.post('/mall/client/address/set_default/' + id)
}

export const getAddressDefaultData = () => {
	return http.get('/mall/client/address/default')
}

export const getAddressInfoData = (id) => {
	return http.get('/mall/client/address/info/' + id)
}
