import http from "@/common/http"

export const getUserOrderAmountData = () => {
	return http.get('mall/client/order/user_amount')
}

export const putOrderData = (data) => {
	return http.put('mall/client/order/add', data)
}

export const getOrderListPageData = (params) => {
	return http.get('mall/client/order/list', {params})
}

export const postOrderCancelData = (id) => {
	return http.post(`mall/client/order/cancel/${id}`)
}

export const deleteOrderData = (id) => {
	return http.delete(`mall/client/order/delete/${id}`)
}

export const getOrderInfoData = (id) => {
	return http.get(`mall/client/order/info/${id}`)
}

export const postOrderReceiptData = (id) => {
	return http.post(`mall/client/order/confirm/${id}`)
}

export function postOrderCommentData (data) {
	return http.post(`mall/client/order/comment`, data)
}
