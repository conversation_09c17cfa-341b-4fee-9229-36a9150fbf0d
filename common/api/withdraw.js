import http from "@/common/http";

export function getWithdrawCardListData () {
	return http.get('/mall/client/withdraw/card/list')
}

export function putWithdrawCardData (data) {
	return http.put('/mall/client/withdraw/card/add', data)
}

export function postWithdrawCardData (data) {
	return http.post('/mall/client/withdraw/card/update', data)
}

export function deleteWithdrawCardData (id) {
	return http.delete('/mall/client/withdraw/card/delete/' + id)
}

export function getWithdrawCardData (id) {
	return http.get('/mall/client/withdraw/card/info/' + id)
}

export function getWithdrawMoneyAvailableData () {
	return http.get('/mall/client/withdraw/money/available')
}

export function putWithdrawApplyData (data) {
	return http.put('/mall/client/withdraw/apply', data)
}

export function getWithdrawTotalData () {
	return http.get('/mall/client/withdraw/total')
}

export function getWithdrawListData () {
	return http.get('/mall/client/withdraw/list')
}

export function getWithdrawIncomeTotalData () {
	return http.get('/mall/client/withdraw/income/total')
}

export function getWithdrawLogInfoData (id) {
	return http.get('/mall/client/withdraw/info/' + id)
}
