// src/utils/orderStatus.ts

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待付款
  PENDING_SHIPMENT = 2, // 待发货
  PENDING_RECEIPT = 3,  // 待收货
  COMPLETED = 4,       // 已完成
  CANCELLED = 5,       // 已取消
}

/**
 * 订单状态描述映射
 */
export const ORDER_STATUS_MAP: Record<OrderStatus, string> = {
  [OrderStatus.PENDING_PAYMENT]: '待付款',
  [OrderStatus.PENDING_SHIPMENT]: '待发货',
  [OrderStatus.PENDING_RECEIPT]: '待收货',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
};

/**
 * 获取订单状态描述
 * @param status 订单状态码
 * @returns 订单状态的中文描述
 */
export function getOrderStatusText(status: number): string {
  return ORDER_STATUS_MAP[status as OrderStatus] || '未知状态';
}

/**
 * 检查订单是否为指定状态
 * @param status 当前订单状态
 * @param targetStatus 目标状态
 * @returns 布尔值，指示是否匹配
 */
export function isOrderStatus(status: number, targetStatus: OrderStatus): boolean {
  return status === targetStatus;
}

/**
 * todo
 * 获取订单状态对应的类名
 * @param status 订单状态码
 * @returns 用于样式的类名
 */
export function getOrderStatusClass(status: number): string {
  const classMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING_PAYMENT]: 'status-warning',
    [OrderStatus.PENDING_SHIPMENT]: 'status-primary',
    [OrderStatus.PENDING_RECEIPT]: 'status-info',
    [OrderStatus.COMPLETED]: 'status-success',
    [OrderStatus.CANCELLED]: 'status-default',
  };

  return classMap[status as OrderStatus] || 'status-default';
}

/**
 * 获取订单的可执行操作
 * @param status 订单状态码
 * @returns 可执行操作的数组
 */
export function getOrderActions(status: number): string[] {
  const actionMap: Record<OrderStatus, string[]> = {
    [OrderStatus.PENDING_PAYMENT]: ['去付款', '取消订单'],
    [OrderStatus.PENDING_SHIPMENT]: ['提醒发货'],
    [OrderStatus.PENDING_RECEIPT]: ['查看物流', '确认收货'],
    [OrderStatus.COMPLETED]: ['再次购买', '去评价'],
    [OrderStatus.CANCELLED]: ['删除订单', '再次购买'],
  };

  return actionMap[status as OrderStatus] || [];
}
