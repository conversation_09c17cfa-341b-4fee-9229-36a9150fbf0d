import Request from 'luch-request'
import CONFIG from './config'
import {useUserStore} from "@/stores/user";

const queryList = []
const userStore = useUserStore()

/*普通请求实例*/
const http = new Request()
http.setConfig(config => {
	config.baseURL = CONFIG.baseHttpUrl
	return config
})
http.interceptors.request.use(
	config => {
		
		const TOKEN = userStore.token
		
		//上传使用前端直传无需过多配置header
		if (config.method !== 'UPLOAD') {
			// 设置租户
			config.header = {
				...config.header,
				'TENANT-ID': CONFIG.tenantID
			}
			// 设置token
			if(TOKEN && !config.header.skipToken){
				config.header.Authorization = `Bearer ${TOKEN}`
			}
		}
		// 请求报文加密 todo:需要时再添加
		
		// 重复请求拦截
		let {url, method, params, data} = config;
		const queryFlag = [url, method, JSON.stringify(params), JSON.stringify(data)].join('&')
		config.custom.queryFlag = queryFlag
		if (queryList.includes(queryFlag)) {
			console.log("被拦截")
			return Promise.reject(config)
		} else {
			queryList.push(queryFlag)
		}
		
		return config
	},
	config => {
		return Promise.reject(config)
	}
)
http.interceptors.response.use(
	response => {
		if (response.config.custom && response.config.custom.queryFlag) {
			queryList.splice(queryList.findIndex(item => item === response.config.custom.queryFlag), 1)
		}
		// 针对pigx后端响应
		if (response.data && response.data.code === 0) {
			return response.data.data
		} else if (!response.data.msg) {
			return response.data
		} else {
			return Promise.reject(response.data.msg)
		}
	},
	response => {
		if (response.config && response.config.custom.queryFlag) {
			queryList.splice(queryList.findIndex(item => item === response.config.custom.queryFlag), 1)
		}
		if ((response.data && response.data.code === 401) || response.statusCode === 403 || response.statusCode === 424) {
			const userStore = useUserStore()
			userStore.logout()
			userStore.showLogin()
			uni.showToast({title: response.data.msg, icon: 'none'})
		}
		return Promise.reject(response)
	}
)
export default http
