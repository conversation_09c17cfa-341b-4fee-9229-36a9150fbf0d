import {defineStore} from 'pinia'
import {ref} from 'vue'

export const useTimerStore = defineStore('timer', () => {
	const smsCountDown = ref(0)
	const smsCountDownStart = (time) => {
		smsCountDown.value = time
		const timer = setInterval(() => {
			if (smsCountDown.value > 0) {
				smsCountDown.value--
			} else {
				clearInterval(timer)
			}
		}, 1000)
	}
	
	return {
		smsCountDown,
		smsCountDownStart
	}
})
