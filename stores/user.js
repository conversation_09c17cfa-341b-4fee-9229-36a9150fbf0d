import {defineStore} from 'pinia';
import {ref} from "vue"

export const useUserStore = defineStore('user', () => {
	/*state*/
	const isLogin = ref(false)
	const token = ref('')
	const openid = ref('')
	const loginVisible = ref(false)
	
	
	/*mutations*/
	const login = () => {
		isLogin.value = true
	}
	const showLogin = () => {
		loginVisible.value = true
	}
	const hideLogin = () => {
		loginVisible.value = false
	}
	const setToken = () => {
		token.value = uni.getStorageSync("userToken")
		isLogin.value = !!token.value
	}
	
	const setOpenid = (val) => {
		uni.setStorageSync("openid", val)
		openid.value = val
	}
	
	const getOpenid = () => {
		if (!openid.value) {
			openid.value = uni.getStorageSync("openid")
		}
		return openid.value
	}
	const logout = () => {
		uni.removeStorageSync("userToken")
		uni.removeStorageSync("refreshToken")
		token.value = ''
		isLogin.value = false
		console.log("用户已退出")
	}
	
	return {
		isLogin,
		token,
		openid,
		loginVisible,
		
		showLogin,
		hideLogin,
		setToken,
		setOpenid,
		getOpenid,
		login,
		logout
	}
})
